# 工费和分佣相关接口文档

## 工费相关接口

### 1. 客户工费优惠管理接口 (Web端)
**Controller**: `CustomerProcessFeeDiscountController`
**路径**: `/member/customer/discount`

| 接口名称 | 方法名 | 请求方式 | 路径 | 描述 |
|---------|--------|----------|------|------|
| 同步客户工费优惠数据 | syncProcessFeeDiscount | POST | /sync | 外部系统调用，同步工费优惠数据 |
| 分页查询客户工费优惠列表 | getPageList | POST | /getPageList | 查询工费优惠配置列表 |
| 查看客户工费优惠详情 | getDetail | GET | /detail | 查看工费优惠详情信息 |
| 新增客户工费优惠 | add | POST | /add | 新增工费优惠配置 |
| 编辑客户工费优惠 | update | POST | /update | 编辑工费优惠配置 |
| 删除客户工费优惠 | delete | POST | /delete | 删除工费优惠配置 |
| 启用客户工费优惠 | enable | POST | /enable | 启用工费优惠配置 |
| 停用客户工费优惠 | disable | POST | /disable | 停用工费优惠配置 |

### 2. 客户工费优惠接口 (移动端)
**Controller**: `MobileMemberCustomerProcessFeeDiscountController`
**路径**: `/member/mobile/customer/discount`

| 接口名称 | 方法名 | 请求方式 | 路径 | 描述 |
|---------|--------|----------|------|------|
| 计算工费优惠 | calculateDiscount | POST | /calculateDiscount | 计算商品工费优惠金额 |

### 3. 客户工费优惠接口 (API服务)
**Controller**: `CustomerProcessFeeDiscountController` (api-service)
**路径**: `/api/member`

| 接口名称 | 方法名 | 请求方式 | 路径 | 描述 |
|---------|--------|----------|------|------|
| 同步客户工费优惠数据 | syncProcessFeeDiscount | POST | /customerProcessFeeDiscount/sync | 外部系统调用接口 |

### 4. 工费优惠服务接口
**Service**: `ICustomerProcessFeeDiscountService`

| 方法名 | 描述 |
|--------|------|
| sync | 同步客户工费优惠数据 |
| getPageList | 分页查询客户工费优惠列表 |
| getDetail | 查看客户工费优惠详情 |
| add | 新增客户工费优惠 |
| update | 编辑客户工费优惠 |
| delete | 删除客户工费优惠 |
| enable | 启用客户工费优惠 |
| disable | 停用客户工费优惠 |
| calculateDiscount | 计算工费优惠 |

---

## 分佣相关接口

### 1. 分佣账户管理接口 (Web端)
**Controller**: `CommissionAccountController`
**路径**: `/member/commission/account`

| 接口名称 | 方法名 | 请求方式 | 路径 | 描述 |
|---------|--------|----------|------|------|
| 分页查询分佣账户列表 | getCommissionAccountPage | POST | /getCommissionAccountPage | 查询分佣账户列表 |
| 冻结客户分佣账户 | freezeCommissionAccount | POST | /freeze | 冻结分佣账户 |
| 解冻客户分佣账户 | unfreezeCommissionAccount | POST | /unfreeze | 解冻分佣账户 |
| 分页查询佣金明细记录列表 | getCommissionDetailPage | POST | /getCommissionDetailPage | 查询佣金明细记录 |
| 绑定银行卡 | bindBankCard | POST | /bindBankCard | 绑定银行卡 |
| 查询银行卡列表 | getBankCardList | GET | /getBankCardList | 查询银行卡列表 |

### 2. 分佣提现管理接口 (Web端)
**Controller**: `CommissionWithdrawalController`
**路径**: `/member/commission/withdrawal`

| 接口名称 | 方法名 | 请求方式 | 路径 | 描述 |
|---------|--------|----------|------|------|
| 分页查询提现记录列表 | getCommissionWithdrawalPage | POST | /getCommissionWithdrawalPage | 查询提现记录列表 |
| 分页查询提现管理列表 | getWithdrawalManagementPage | POST | /getWithdrawalManagementPage | 查询提现管理列表 |
| 用户申请提现 | applyWithdrawal | POST | /apply | 用户申请提现 |
| 审核提现申请 | approveWithdrawal | POST | /approve | 审核提现申请 |
| 财务打款 | processPayment | POST | /payment | 财务打款处理 |

### 3. 分佣配置管理接口 (Web端)
**Controller**: `CommissionConfigController`
**路径**: `/member/commission/config`

| 接口名称 | 方法名 | 请求方式 | 路径 | 描述 |
|---------|--------|----------|------|------|
| 查询分佣配置 | getCommissionConfig | GET | /get | 查询分佣配置信息 |
| 更新邀请奖励配置 | updateInvitationConfig | POST | /invitation/update | 更新邀请奖励配置 |
| 更新订单佣金配置 | updateOrderCommissionConfig | POST | /orderCommission/update | 更新订单佣金配置 |
| 更新提现配置 | updateWithdrawalConfig | POST | /withdrawal/update | 更新提现配置 |

### 4. 小程序分佣接口 (移动端)
**Controller**: `MiniProgramCommissionController`
**路径**: `/member/mobile/commission`

| 接口名称 | 方法名 | 请求方式 | 路径 | 描述 |
|---------|--------|----------|------|------|
| 查询分佣账户信息 | getCommissionAccountInfo | GET | /account/info | 查询账户信息 |
| 查询邀请客户列表 | getInvitedCustomerPage | POST | /invited/customers | 查询邀请客户列表 |
| 分页查询佣金明细记录 | getCommissionDetailPage | POST | /details | 查询佣金明细 |
| 分页查询提现记录 | getCommissionWithdrawalPage | POST | /withdrawals | 查询提现记录 |
| 申请提现 | applyWithdrawal | POST | /withdrawal/apply | 申请提现 |
| 绑定银行卡 | bindBankCard | POST | /bankCard/bind | 绑定银行卡 |
| 查询银行卡列表 | getBankCardList | GET | /bankCard/list | 查询银行卡列表 |

### 5. 分佣账户服务接口
**Service**: `ICommissionAccountService`

| 方法名 | 描述 |
|--------|------|
| getCommissionAccountPage | 分页查询分佣账户列表 |
| freezeCommissionAccount | 冻结客户分佣账户 |
| unfreezeCommissionAccount | 解冻客户分佣账户 |
| getCommissionDetailPage | 分页查询佣金明细记录列表 |
| bindBankCard | 绑定银行卡 |
| getBankCardList | 查询银行卡列表 |

### 6. 分佣提现服务接口
**Service**: `ICommissionWithdrawalService`

| 方法名 | 描述 |
|--------|------|
| getCommissionWithdrawalPage | 分页查询佣金提现记录列表 |
| getWithdrawalManagementPage | 分页查询提现管理列表 |
| approveWithdrawal | 审核提现申请 |
| processPayment | 财务打款 |
| applyWithdrawal | 用户申请提现 |

---

## 备注说明

### 工费相关功能特点：
- 支持客户工费优惠配置的完整生命周期管理
- 提供工费优惠计算功能，支持基本工费、克工费、件工费的优惠计算
- 支持外部系统数据同步
- 每个客户只能有一个启用的工费优惠配置

### 分佣相关功能特点：
- 完整的分佣账户管理体系
- 支持银行卡绑定和管理
- 提供提现申请、审核、打款的完整流程
- 支持账户冻结/解冻操作
- 提供详细的佣金明细记录查询
- 支持分佣配置的灵活管理
- 小程序端提供完整的分佣功能支持
