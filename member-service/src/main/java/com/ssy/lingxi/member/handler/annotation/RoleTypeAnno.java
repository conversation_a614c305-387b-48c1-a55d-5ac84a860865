package com.ssy.lingxi.member.handler.annotation;

import com.ssy.lingxi.member.handler.validator.RoleTypeValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

@Target({ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE, ElementType.CONSTRUCTOR, ElementType.PARAMETER})
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = {RoleTypeValidator.class})
public @interface RoleTypeAnno {
    boolean required() default true;

    String message() default "参数值不在枚举定义范围内";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
