package com.ssy.lingxi.member.serviceImpl.web;

import com.alibaba.fastjson.JSONObject;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.req.api.member.CustomerProcessFeeDiscountSyncReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.product.CommoditySaleModeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.member.api.enums.GramLaborDiscountTypeEnum;
import com.ssy.lingxi.member.api.enums.PieceLaborDiscountTypeEnum;
import com.ssy.lingxi.member.api.feign.IMemberFeign;
import com.ssy.lingxi.member.api.model.req.CalDiscountSkuReq;
import com.ssy.lingxi.member.api.model.req.CustomerCalReq;
import com.ssy.lingxi.member.api.model.req.GetMemberByIdReq;
import com.ssy.lingxi.member.api.model.resp.MemberFeignCodeRes;
import com.ssy.lingxi.member.api.model.resp.MobileCustomerFeeDiscountResp;
import com.ssy.lingxi.member.entity.bo.CustomerProcessFeeDiscountBO;
import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.entity.do_.discount.CustomerProcessFeeDiscountBtDO;
import com.ssy.lingxi.member.entity.do_.discount.CustomerProcessFeeDiscountDO;
import com.ssy.lingxi.member.entity.do_.discount.CustomerProcessFeeDiscountDO_;
import com.ssy.lingxi.member.entity.do_.discount.CustomerProcessFeeDiscountFtDO;
import com.ssy.lingxi.member.enums.DataSourceEnum;
import com.ssy.lingxi.member.handler.converter.CustomerProcessFeeDiscountMapper;
import com.ssy.lingxi.member.model.req.discount.web.CustomerProcessFeeDiscountPageReq;
import com.ssy.lingxi.member.model.req.discount.web.CustomerProcessFeeDiscountReq;
import com.ssy.lingxi.member.model.resp.customer.CustomerProcessFeeDiscountDetailResp;
import com.ssy.lingxi.member.model.resp.customer.CustomerProcessFeeDiscountPageResp;
import com.ssy.lingxi.member.repository.CustomerProcessFeeDiscountRepository;
import com.ssy.lingxi.member.repository.MemberRepository;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.web.ICustomerProcessFeeDiscountService;
import com.ssy.lingxi.product.api.feign.ICommodityFeign;
import com.ssy.lingxi.product.api.model.resp.commodity.CommodityExtraDataParamResp;
import com.ssy.lingxi.product.api.model.resp.commodity.CommoditySkuResp;
import com.ssy.lingxi.product.api.model.resp.commodity.CustomerCategoryResp;
import com.ssy.lingxi.product.api.model.resp.commodity.FreightSpaceSingleProductResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 客户工费优惠服务实现类
 */
@Service
@Slf4j
public class CustomerProcessFeeDiscountServiceImpl implements ICustomerProcessFeeDiscountService {

    @Resource
    private CustomerProcessFeeDiscountRepository customerProcessFeeDiscountRepository;

    @Resource
    private ICommodityFeign commodityFeign;

    @Resource
    private IBaseMemberCacheService memberCacheService;

    @Resource
    private IMemberFeign memberFeign;

    @Resource
    private MemberRepository memberRepository;

    private final CustomerProcessFeeDiscountMapper discountMapper = CustomerProcessFeeDiscountMapper.INSTANCE;


    private Boolean saveByBO(CustomerProcessFeeDiscountBO discountBO) {
        // 将业务对象转换为数据库对象
        CustomerProcessFeeDiscountDO discountDO = discountMapper.boToDdo(discountBO);

        // 设置明细关联关系
        setDetailRelations(discountDO);

        // 只对数据同步来源的数据进行编码转换
        if (DataSourceEnum.INTERFACE_SYNC.getCode().equals(discountBO.getDataSource())) {
            convertExternalCodes(discountDO);
        }

        // 保存或更新数据
        return saveOrUpdateDiscount(discountDO, discountBO.getDocumentSerialNumber());
    }

    /**
     * 设置明细与主表的关联关系
     */
    private void setDetailRelations(CustomerProcessFeeDiscountDO discountDO) {
        if (discountDO == null) {
            log.warn("主对象为空，无法设置关联关系");
            return;
        }

        // 设置表体明细关联关系
        if (!CollectionUtils.isEmpty(discountDO.getBtContent())) {
            discountDO.getBtContent().forEach(btDO -> {
                if (btDO != null) {
                    btDO.setCustomerProcessFeeDiscount(discountDO);
                }
            });
            log.debug("设置表体明细关联关系，数量：{}", discountDO.getBtContent().size());
        }

        // 设置附体明细关联关系
        if (!CollectionUtils.isEmpty(discountDO.getFtContent())) {
            discountDO.getFtContent().forEach(ftDO -> {
                if (ftDO != null) {
                    ftDO.setCustomerProcessFeeDiscount(discountDO);
                }
            });
            log.debug("设置附体明细关联关系，数量：{}", discountDO.getFtContent().size());
        }
    }

    /**
     * 转换外部编码为系统内部编码（仅对数据同步来源的数据进行转换）
     */
    private void convertExternalCodes(CustomerProcessFeeDiscountDO discountDO) {
        // 转换表体明细中的物品分类编码
//        convertBtItemCategories(discountDO.getBtContent());

        // 转换附体明细中的物品编码和材质编码
//        convertFtItemAndMaterialCodes(discountDO.getFtContent());

        // 将客户编码转换一下
        String outCustomerCode = discountDO.getOutCustomerCode();
        convertCustomerCode(discountDO, outCustomerCode);
    }

    /**
     * 转换外部客户编码为内部客户ID和客户名称
     *
     * @param discountDO      客户工费优惠对象
     * @param outCustomerCode 外部客户编码（对应member表的account字段）
     */
    private void convertCustomerCode(CustomerProcessFeeDiscountDO discountDO, String outCustomerCode) {
        if (!StringUtils.hasLength(outCustomerCode)) {
            log.warn("未找到外部客户编码对应的会员信息，外部客户编码：{}", outCustomerCode);
            throw new BusinessException("客户编码映射失败");
        }
        // 根据account查询会员信息
        MemberDO member = memberRepository.findFirstByCode(outCustomerCode);
        if (member != null) {
            // 设置转换后的客户ID和客户名称
            discountDO.setCustomerId(member.getId());
            discountDO.setCustomerName(member.getName());
            discountDO.setCustomerCode(member.getCode());
            log.debug("客户编码转换成功：外部编码 {} -> 内部ID {}，客户名称：{}", outCustomerCode, member.getId(), member.getName());
        } else {
            log.warn("未找到外部客户编码对应的会员信息，外部客户编码：{}", outCustomerCode);
            throw new BusinessException("客户编码映射失败");
        }

    }


    /**
     * 保存或更新优惠数据
     */
    private Boolean saveOrUpdateDiscount(CustomerProcessFeeDiscountDO discountDO, Long documentSerialNumber) {
        // 查询是否已存在相同单据流水号的记录
        Optional<CustomerProcessFeeDiscountDO> existingDOOptional = customerProcessFeeDiscountRepository.findByDocumentSerialNumber(documentSerialNumber);

        if (existingDOOptional.isPresent()) {
            // 更新现有记录
            CustomerProcessFeeDiscountDO existingDO = existingDOOptional.get();

            // 清除旧的子集合关联，避免重复关联
            if (existingDO.getBtContent() != null) {
                existingDO.getBtContent().clear();
            }
            if (existingDO.getFtContent() != null) {
                existingDO.getFtContent().clear();
            }

            // 设置主键ID和时间字段
            discountDO.setId(existingDO.getId());
            discountDO.setCreateTime(existingDO.getCreateTime());
            discountDO.setUpdateTime(LocalDateTime.now());

            // 重新设置关联关系
            setDetailRelations(discountDO);

            log.info("更新客户工费优惠数据，单据流水号：{}", documentSerialNumber);
        } else {
            // 新增记录
            discountDO.setCreateTime(LocalDateTime.now());
            discountDO.setUpdateTime(LocalDateTime.now());
            discountDO.setStatus(0);

            // 设置关联关系
            setDetailRelations(discountDO);

            log.info("新增客户工费优惠数据，单据流水号：{}", documentSerialNumber);
        }

        // 保存数据
        customerProcessFeeDiscountRepository.save(discountDO);
        log.info("客户工费优惠数据保存成功，单据流水号：{}", documentSerialNumber);

        return Boolean.TRUE;
    }

    @Override
    public Boolean sync(CustomerProcessFeeDiscountSyncReq customerProcessFeeDiscountSyncReq) {
        //将customerProcessFeeDiscountSyncReq转换为CustomerProcessFeeDiscountBO，根据JsonProperty 对应的参数进行转换
        CustomerProcessFeeDiscountBO customerProcessFeeDiscountBO = discountMapper.syncReqToBo(customerProcessFeeDiscountSyncReq);
        //同步过来的有效期都设置成2099年
        LocalDateTime discountEndTime = LocalDateTime.of(2099, 12, 31, 23, 59, 59);
        customerProcessFeeDiscountBO.setDiscountEndTime(discountEndTime);
        customerProcessFeeDiscountBO.getBtContent().forEach(btDO -> {
            btDO.setDiscountEndTime(discountEndTime);
        });
        //设置数据来源
        customerProcessFeeDiscountBO.setDataSource(DataSourceEnum.INTERFACE_SYNC.getCode());

        return saveByBO(customerProcessFeeDiscountBO);
    }

    @Override
    public PageDataResp<CustomerProcessFeeDiscountPageResp> getPageList(HttpHeaders headers, CustomerProcessFeeDiscountPageReq pageReq) {
        log.info("分页查询客户工费优惠列表，查询条件：{}", pageReq);

        // 构建分页参数
        Pageable pageable = PageRequest.of(pageReq.getCurrent() - 1, pageReq.getPageSize(), Sort.by(Sort.Direction.DESC, CustomerProcessFeeDiscountDO_.ID));

        // 构建查询条件
        Specification<CustomerProcessFeeDiscountDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();


            // 客户名称模糊查询
            if (StringUtils.hasText(pageReq.getCustomerName())) {
                predicates.add(criteriaBuilder.like(root.get(CustomerProcessFeeDiscountDO_.CUSTOMER_NAME), "%" + pageReq.getCustomerName() + "%"));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };

        // 执行查询
        Page<CustomerProcessFeeDiscountDO> page = customerProcessFeeDiscountRepository.findAll(specification, pageable);

        // 转换为响应对象
        List<CustomerProcessFeeDiscountPageResp> resultList = new ArrayList<>();
        for (CustomerProcessFeeDiscountDO discountDO : page.getContent()) {
            CustomerProcessFeeDiscountPageResp resp = discountMapper.doToPageResp(discountDO);
            resultList.add(resp);
        }

        return new PageDataResp<>(page.getTotalElements(), resultList);
    }

    @Override
    public CustomerProcessFeeDiscountDetailResp getDetail(HttpHeaders headers, CommonIdReq idReq) {
        log.info("查看客户工费优惠详情，ID：{}", idReq.getId());

        // 查询数据
        CustomerProcessFeeDiscountDO discountDO = customerProcessFeeDiscountRepository.findById(idReq.getId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.RECORDS_DON_T_EXIST, "客户工费优惠记录不存在"));

        // 由于使用了LAZY加载，需要显式触发加载子集合
        // 这样可以确保btContent和ftContent被正确加载
        if (discountDO.getBtContent() != null) {
            discountDO.getBtContent().size(); // 触发懒加载
        }
        if (discountDO.getFtContent() != null) {
            discountDO.getFtContent().size(); // 触发懒加载
        }

        // 转换为响应对象
        CustomerProcessFeeDiscountDetailResp detailResp = discountMapper.doToDetailResp(discountDO);

        // 填充关联信息
        fillRelatedInfo(detailResp);

        log.info("查看客户工费优惠详情成功，ID：{}，表体明细数量：{}，附体明细数量：{}", idReq.getId(), detailResp.getBtContent() != null ? detailResp.getBtContent().size() : 0, detailResp.getFtContent() != null ? detailResp.getFtContent().size() : 0);

        return detailResp;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean add(HttpHeaders headers, CustomerProcessFeeDiscountReq req) {
        log.info("新增客户工费优惠，参数：{}", req);

        // 如果要启用该配置，检查客户是否已有启用的工费配置
        if (req.getStatus() != null && req.getStatus() == 1) {
            List<CustomerProcessFeeDiscountDO> existingEnabledConfigs = customerProcessFeeDiscountRepository.findByCustomerIdAndStatus(req.getCustomerId(), 1);
            if (!existingEnabledConfigs.isEmpty()) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_CUSTOMER_ALREADY_HAS_ENABLED_PROCESS_FEE_DISCOUNT, "客户【" + req.getCustomerName() + "】已有启用的工费优惠配置");
            }
        }
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        // 使用MapStruct转换为DO对象
        CustomerProcessFeeDiscountDO discountDO = discountMapper.webReqToDO(req);

        discountDO.setCreator(loginUser.getUserName());
        discountDO.setCreatorId(loginUser.getUserId().toString());
        // 设置明细关联关系 - 关键步骤！
        setDetailRelations(discountDO);

        // 设置创建时间和数据来源
        LocalDateTime now = LocalDateTime.now();
        discountDO.setCreateTime(now);
        discountDO.setUpdateTime(now);
        discountDO.setDataSource(DataSourceEnum.MANUAL_ENTRY.getCode());

        // 生成单据流水号（这里可以根据业务规则生成）
        discountDO.setDocumentSerialNumber(System.currentTimeMillis());

        // 保存数据
        customerProcessFeeDiscountRepository.save(discountDO);

        log.info("新增客户工费优惠成功，ID：{}", discountDO.getId());
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(HttpHeaders headers, com.ssy.lingxi.member.model.req.discount.web.CustomerProcessFeeDiscountReq req) {
        log.info("编辑客户工费优惠，参数：{}", req);

        // 检查ID是否存在
        if (req.getId() == null) {
            throw new BusinessException(ResponseCodeEnum.REQUEST_PARAM_CHECK_FAILED, "更新时ID不能为空");
        }

        // 查询现有记录
        CustomerProcessFeeDiscountDO existingDO = customerProcessFeeDiscountRepository.findById(req.getId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.RECORDS_DON_T_EXIST, "客户工费优惠记录不存在"));

        // 如果要启用该配置，检查客户是否已有其他启用的工费配置
        if (req.getStatus() != null && req.getStatus() == 1) {
            boolean hasEnabledConfig = customerProcessFeeDiscountRepository.existsByCustomerIdAndStatusAndIdNot(req.getCustomerId(), 1, req.getId());
            if (hasEnabledConfig) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_CUSTOMER_ALREADY_HAS_ENABLED_PROCESS_FEE_DISCOUNT, "客户【" + req.getCustomerName() + "】已有启用的工费优惠配置");
            }
        }

        // 使用MapStruct转换并更新字段
        CustomerProcessFeeDiscountDO updatedDO = discountMapper.webReqToDO(req);

        // 确保 isEnabled 字段与 status 字段保持一致
//        if (updatedDO.getStatus() != null) {
//            updatedDO.setIsEnabled(updatedDO.getStatus() == 1);
//        }

        // 设置明细关联关系 - 关键步骤！
        setDetailRelations(updatedDO);

        // 保留原有的创建时间、单据流水号和数据来源
        updatedDO.setCreateTime(existingDO.getCreateTime());
        updatedDO.setDocumentSerialNumber(existingDO.getDocumentSerialNumber());
        updatedDO.setDataSource(existingDO.getDataSource());
        updatedDO.setUpdateTime(LocalDateTime.now());

        // 保存数据
        customerProcessFeeDiscountRepository.save(updatedDO);

        log.info("编辑客户工费优惠成功，ID：{}", req.getId());
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delete(HttpHeaders headers, CommonIdReq idReq) {
        log.info("删除客户工费优惠，ID：{}", idReq.getId());

        // 检查记录是否存在
        if (!customerProcessFeeDiscountRepository.existsById(idReq.getId())) {
            throw new BusinessException(ResponseCodeEnum.RECORDS_DON_T_EXIST, "客户工费优惠记录不存在");
        }

        // 删除记录
        customerProcessFeeDiscountRepository.deleteById(idReq.getId());

        log.info("删除客户工费优惠成功，ID：{}", idReq.getId());
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean enable(HttpHeaders headers, CommonIdListReq idListReq) {
        log.info("批量启用客户工费优惠，ID列表：{}", idListReq.getIdList());

        // 查询所有记录
        List<CustomerProcessFeeDiscountDO> discountDOList = customerProcessFeeDiscountRepository.findAllById(idListReq.getIdList());

        if (discountDOList.size() != idListReq.getIdList().size()) {
            throw new BusinessException(ResponseCodeEnum.RECORDS_DON_T_EXIST, "部分客户工费优惠记录不存在");
        }

        // 检查每个客户是否已有启用的工费配置
        for (CustomerProcessFeeDiscountDO discountDO : discountDOList) {
            boolean hasEnabledConfig = customerProcessFeeDiscountRepository.existsByCustomerIdAndStatus(discountDO.getCustomerId(), 1);
            if (hasEnabledConfig) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_CUSTOMER_ALREADY_HAS_ENABLED_PROCESS_FEE_DISCOUNT, "客户【" + discountDO.getCustomerName() + "】已有启用的工费优惠配置");
            }
        }

        // 批量更新状态
        LocalDateTime now = LocalDateTime.now();
        discountDOList.forEach(discountDO -> {
//            discountDO.setIsEnabled(Boolean.TRUE);
            discountDO.setStatus(1);
            discountDO.setUpdateTime(now);
        });

        // 保存数据
        customerProcessFeeDiscountRepository.saveAll(discountDOList);

        log.info("批量启用客户工费优惠成功，共{}条记录", discountDOList.size());
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean disable(HttpHeaders headers, CommonIdListReq idListReq) {
        log.info("批量停用客户工费优惠，ID列表：{}", idListReq.getIdList());

        // 查询所有记录
        List<CustomerProcessFeeDiscountDO> discountDOList = customerProcessFeeDiscountRepository.findAllById(idListReq.getIdList());

        if (discountDOList.size() != idListReq.getIdList().size()) {
            throw new BusinessException(ResponseCodeEnum.RECORDS_DON_T_EXIST, "部分客户工费优惠记录不存在");
        }

        // 批量更新状态
        LocalDateTime now = LocalDateTime.now();
        discountDOList.forEach(discountDO -> {
//            discountDO.setIsEnabled(Boolean.FALSE);
            discountDO.setStatus(0);
            discountDO.setUpdateTime(now);
        });

        // 保存数据
        customerProcessFeeDiscountRepository.saveAll(discountDOList);

        log.info("批量停用客户工费优惠成功，共{}条记录", discountDOList.size());
        return Boolean.TRUE;
    }


    @Override
    public List<MobileCustomerFeeDiscountResp> calculateDiscount(CustomerCalReq customerCalReq) {
        String logPrefix = "计算客户工费优惠，客户id: " + customerCalReq.getCustomerId();

        log.info("{} - 开始计算，入参: {}", logPrefix, JSONObject.toJSONString(customerCalReq));

        List<MobileCustomerFeeDiscountResp> resultList = new ArrayList<>();

        try {
            // 1. 查询客户的工费优惠配置
            log.info("{} - 步骤1：查询客户工费优惠配置", logPrefix);
            List<CustomerProcessFeeDiscountDO> customerDiscounts = findCustomerDiscounts(customerCalReq.getCustomerId());

            if (CollectionUtils.isEmpty(customerDiscounts)) {
                log.info("{} - 客户未匹配到工费优惠配置，返回空结果", logPrefix);
                return resultList;
            }
            log.info("{} - 查询到{}条有效的工费优惠配置", logPrefix, customerDiscounts.size());

            // 2. 为每个商品计算工费优惠
            log.info("{} - 步骤2：开始计算{}个商品的工费优惠", logPrefix, customerCalReq.getSkuIdList().size());
            for (int i = 0; i < customerCalReq.getSkuIdList().size(); i++) {
                CalDiscountSkuReq skuReq = customerCalReq.getSkuIdList().get(i);
                log.info("{} - 计算第{}个商品，单件编码: {}, skuId: {}", logPrefix, i + 1, skuReq.getSingleCode(), skuReq.getSkuId());

                MobileCustomerFeeDiscountResp discountResp = calculateSingleProductDiscount(skuReq, customerDiscounts);
                resultList.add(discountResp);

                log.info("{} - 第{}个商品计算完成，单件编码: {}", logPrefix, i + 1, skuReq.getSingleCode());
            }

            log.info("{} - 计算完成，出参: {}", logPrefix, JSONObject.toJSONString(resultList));
            return resultList;

        } catch (Exception e) {
            log.error("{} - 计算过程中发生异常: {}", logPrefix, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 填充关联信息：客户名称、分类名称、商品名称
     */
    private void fillRelatedInfo(CustomerProcessFeeDiscountDetailResp detailResp) {
        try {
            // 填充客户名称
            fillCustomerName(detailResp);

            // 填充表体明细中的分类名称
            fillCategoryNames(detailResp);

            // 填充附体明细中的商品名称
            fillItemNames(detailResp);

        } catch (Exception e) {
            log.warn("填充关联信息失败，错误信息：{}", e.getMessage(), e);
        }
    }

    /**
     * 填充客户名称
     */
    private void fillCustomerName(CustomerProcessFeeDiscountDetailResp detailResp) {
        if (detailResp.getCustomerId() == null) {
            return;
        }

        try {
            GetMemberByIdReq memberReq = new GetMemberByIdReq();
            memberReq.setMemberId(detailResp.getCustomerId());

            MemberFeignCodeRes memberInfo = memberFeign.findMemberById(memberReq).getData();
            if (memberInfo != null && StringUtils.hasText(memberInfo.getName())) {
                detailResp.setCustomerName(memberInfo.getName());
                log.debug("填充客户名称成功，客户ID：{}，客户名称：{}", detailResp.getCustomerId(), memberInfo.getName());
            }
        } catch (Exception e) {
            log.warn("查询客户名称失败，客户ID：{}，错误信息：{}", detailResp.getCustomerId(), e.getMessage());
        }
    }

    /**
     * 填充分类名称
     */
    private void fillCategoryNames(CustomerProcessFeeDiscountDetailResp detailResp) {
        if (CollectionUtils.isEmpty(detailResp.getBtContent())) {
            return;
        }

        // 收集所有需要查询的分类ID
        List<Long> categoryIds = detailResp.getBtContent().stream().filter(bt -> bt.getCategoryId() != null).map(bt -> bt.getCategoryId()).distinct().collect(Collectors.toList());

        if (CollectionUtils.isEmpty(categoryIds)) {
            return;
        }

        try {
            // 批量查询分类信息
            List<CustomerCategoryResp> categoryList = commodityFeign.getCustomerCategoryById(categoryIds).getData();
            if (!CollectionUtils.isEmpty(categoryList)) {
                // 构建分类ID到分类名称的映射
                Map<Long, String> categoryIdToNameMap = categoryList.stream().collect(Collectors.toMap(CustomerCategoryResp::getId, CustomerCategoryResp::getName, (existing, replacement) -> existing));

                // 设置分类名称
                detailResp.getBtContent().forEach(bt -> {
                    if (bt.getCategoryId() != null) {
                        String categoryName = categoryIdToNameMap.get(bt.getCategoryId());
                        if (StringUtils.hasText(categoryName)) {
                            bt.setItemCategory(categoryName);
                            log.debug("填充分类名称成功，分类ID：{}，分类名称：{}", bt.getCategoryId(), categoryName);
                        }
                    }
                });
            }
        } catch (Exception e) {
            log.warn("查询分类名称失败，分类ID列表：{}，错误信息：{}", categoryIds, e.getMessage());
        }
    }

    /**
     * 填充商品名称
     */
    private void fillItemNames(CustomerProcessFeeDiscountDetailResp detailResp) {
        if (CollectionUtils.isEmpty(detailResp.getFtContent())) {
            return;
        }

        // 收集所有需要查询的SKU ID
        List<Long> skuIds = detailResp.getFtContent().stream().filter(ft -> ft.getSkuId() != null).map(ft -> ft.getSkuId()).distinct().collect(Collectors.toList());

        if (CollectionUtils.isEmpty(skuIds)) {
            return;
        }

        try {
            // 逐个查询商品信息（因为接口是单个查询）
            Map<Long, String> skuIdToNameMap = new HashMap<>();
            for (Long skuId : skuIds) {
                try {
                    CommoditySkuResp commodityInfo = commodityFeign.getCommodityBySkuId(skuId).getData();
                    if (commodityInfo != null && StringUtils.hasText(commodityInfo.getName())) {
                        skuIdToNameMap.put(skuId, commodityInfo.getName());
                        log.debug("查询商品信息成功，SKU ID：{}，商品名称：{}", skuId, commodityInfo.getName());
                    }
                } catch (Exception e) {
                    log.warn("查询单个商品信息失败，SKU ID：{}，错误信息：{}", skuId, e.getMessage());
                }
            }

            // 设置商品名称
            detailResp.getFtContent().forEach(ft -> {
                if (ft.getSkuId() != null) {
                    String itemName = skuIdToNameMap.get(ft.getSkuId());
                    if (StringUtils.hasText(itemName)) {
                        ft.setItemName(itemName);
                        log.debug("填充商品名称成功，SKU ID：{}，商品名称：{}", ft.getSkuId(), itemName);
                    }
                }
            });

        } catch (Exception e) {
            log.warn("查询商品名称失败，SKU ID列表：{}，错误信息：{}", skuIds, e.getMessage());
        }
    }

    /**
     * 查询客户的工费优惠配置
     */
    private List<CustomerProcessFeeDiscountDO> findCustomerDiscounts(Long customerId) {
        String logPrefix = "计算客户工费优惠，客户id: " + customerId;

        try {
            log.info("{} - 查询启用状态的客户工费优惠配置", logPrefix);
            // 查询启用状态的客户工费优惠配置
            List<CustomerProcessFeeDiscountDO> discounts = customerProcessFeeDiscountRepository.findByCustomerIdAndStatusOrderByCreateTimeDesc(customerId, 1);
            log.info("{} - 查询到{}条启用状态的配置", logPrefix, discounts.size());

            // 过滤未过期的优惠
            LocalDateTime now = LocalDateTime.now();
            List<CustomerProcessFeeDiscountDO> validDiscounts = discounts.stream()
                    .filter(discount -> discount.getDiscountEndTime() == null || discount.getDiscountEndTime().isAfter(now))
                    .collect(Collectors.toList());

            log.info("{} - 过滤后有效配置{}条", logPrefix, validDiscounts.size());
            return validDiscounts;

        } catch (Exception e) {
            log.error("{} - 查询客户工费优惠配置失败: {}", logPrefix, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 计算单个商品的工费优惠
     * 返回计算前、计算后、优惠差额三组数据，以及克工费每克优惠金额
     * 根据公式计算：
     * 1. 基本工费 = 原基本工费 - 基本工费优惠
     * 2. 附加克工费 = (原附加克工费 - 克工费优惠) × 克工费折扣 - 指定克工费优惠
     * 3. 附加件工费 = (原附加件工费 - 件工费优惠) × 件工费折扣
     */
    private MobileCustomerFeeDiscountResp calculateSingleProductDiscount(CalDiscountSkuReq skuReq, List<CustomerProcessFeeDiscountDO> customerDiscounts) {
        String singleCode = skuReq.getSingleCode();
        String logPrefix = "计算客户工费优惠，客户id: " + customerDiscounts.get(0).getCustomerId() + "，单件编码: " + singleCode;

        log.info("{} - 开始计算单个商品工费优惠，skuId: {}", logPrefix, skuReq.getSkuId());

        // 1. 初始化响应对象
        MobileCustomerFeeDiscountResp discountResp = initializeResponse(singleCode, skuReq.getSkuId());

        // 2. 获取商品信息
        ProductInfo productInfo = getProductInfo(skuReq, discountResp);
        if (productInfo == null) {
            log.warn("{} - 获取商品信息失败，返回默认响应", logPrefix);
            return discountResp;
        }

        // 3. 设置原始工费信息（计算前）
        setOriginalLaborCostsInResponse(discountResp, productInfo);

        // 4. 获取客户工费优惠配置
        CustomerProcessFeeDiscountDO customerDiscount = customerDiscounts.get(0);

        // 5. 计算基本工费优惠
        calculateBaseLaborCostDiscount(discountResp, productInfo, customerDiscount);

        // 6. 计算附加工费优惠（克工费和件工费）
        calculateAdditionalLaborCostDiscount(discountResp, productInfo, customerDiscount);

        // 7. 计算优惠差额
        calculateDiscountAmountsInResponse(discountResp);

        // 8. 计算最终金额（包括原始金额和优惠后金额）
        calculateFinalAmounts(discountResp, productInfo);

        log.info("{} - 计算完成，结果: {}", logPrefix, JSONObject.toJSONString(discountResp));
        return discountResp;


    }


    /**
     * 初始化响应对象
     */
    private MobileCustomerFeeDiscountResp initializeResponse(String singleCode, Long skuId) {
        MobileCustomerFeeDiscountResp discountResp = new MobileCustomerFeeDiscountResp();
        discountResp.setSingleCode(singleCode);
        discountResp.setSkuId(skuId);
        discountResp.setBaseLaborCosts(BigDecimal.ZERO);
        discountResp.setAdditionalLaborCosts(BigDecimal.ZERO);
        discountResp.setPieceLaborCosts(BigDecimal.ZERO);
        discountResp.setAdditionalLaborCostAmount(BigDecimal.ZERO);

        // 初始化原始工费字段
        discountResp.setOriginalBaseLaborCost(BigDecimal.ZERO);
        discountResp.setOriginalAdditionalLaborCost(BigDecimal.ZERO);
        discountResp.setOriginalPieceLaborCost(BigDecimal.ZERO);
        discountResp.setOriginalBaseLaborCostAmount(BigDecimal.ZERO);
        discountResp.setOriginalGramLaborCostAmount(BigDecimal.ZERO);
        discountResp.setOriginalPieceLaborCostAmount(BigDecimal.ZERO);
        discountResp.setOriginalAdditionalLaborCostAmount(BigDecimal.ZERO);

        // 初始化优惠差额字段
        discountResp.setBaseLaborCostDiscount(BigDecimal.ZERO);
        discountResp.setAdditionalLaborCostDiscount(BigDecimal.ZERO);
        discountResp.setPieceLaborCostDiscount(BigDecimal.ZERO);
        discountResp.setBaseLaborCostDiscountAmount(BigDecimal.ZERO);
        discountResp.setGramLaborCostDiscountAmount(BigDecimal.ZERO);
        discountResp.setPieceLaborCostDiscountAmount(BigDecimal.ZERO);
        discountResp.setAdditionalLaborCostDiscountAmount(BigDecimal.ZERO);
        discountResp.setGramLaborCostDiscountPerGram(BigDecimal.ZERO);
        discountResp.setBaseLaborCostDiscountPerGram(BigDecimal.ZERO);

        // 初始化指定克工费相关字段
        discountResp.setSpecifiedGramLaborCost(BigDecimal.ZERO);
        discountResp.setSpecifiedGramLaborCostDiscount(BigDecimal.ZERO);

        return discountResp;
    }

    /**
     * 设置原始工费信息（计算前的工费）
     */
    private void setOriginalLaborCostsInResponse(MobileCustomerFeeDiscountResp discountResp, ProductInfo productInfo) {
        // 直接使用 ProductInfo 中的公共工费信息
        BigDecimal baseLaborCost = productInfo.baseLaborCosts;
        BigDecimal additionalLaborCost = productInfo.additionalLaborCosts;
        BigDecimal pieceLaborCost = productInfo.pieceLaborCosts;

        // 设置原始工费单价
        discountResp.setOriginalBaseLaborCost(ensureNonNull(baseLaborCost));
        discountResp.setOriginalAdditionalLaborCost(ensureNonNull(additionalLaborCost));
        discountResp.setOriginalPieceLaborCost(ensureNonNull(pieceLaborCost));

        log.info("设置原始工费单价完成：基本工费={}, 附加克工费={}, 附加件工费={}", baseLaborCost, additionalLaborCost, pieceLaborCost);
    }

    /**
     * 计算优惠差额
     */
    private void calculateDiscountAmountsInResponse(MobileCustomerFeeDiscountResp discountResp) {
        String singleCode = discountResp.getSingleCode();

        log.info("单件编码: {} - 开始计算优惠差额", singleCode);

        // 计算单价优惠差额
        BigDecimal baseLaborCostDiscount = discountResp.getOriginalBaseLaborCost().subtract(discountResp.getBaseLaborCosts());
        BigDecimal additionalLaborCostDiscount = discountResp.getOriginalAdditionalLaborCost().subtract(discountResp.getAdditionalLaborCosts());
        BigDecimal pieceLaborCostDiscount = discountResp.getOriginalPieceLaborCost().subtract(discountResp.getPieceLaborCosts());

        discountResp.setBaseLaborCostDiscount(ensureNonNegative(baseLaborCostDiscount));
        discountResp.setAdditionalLaborCostDiscount(ensureNonNegative(additionalLaborCostDiscount));
        discountResp.setPieceLaborCostDiscount(ensureNonNegative(pieceLaborCostDiscount));

        // 设置基本工费每克优惠金额（即基本工费的单价优惠差额）
        BigDecimal baseLaborCostDiscountPerGram = discountResp.getBaseLaborCostDiscount();
        discountResp.setBaseLaborCostDiscountPerGram(baseLaborCostDiscountPerGram);

        // 设置克工费每克优惠金额（即附加克工费的单价优惠差额）
        BigDecimal gramLaborCostDiscountPerGram = discountResp.getAdditionalLaborCostDiscount();
        discountResp.setGramLaborCostDiscountPerGram(gramLaborCostDiscountPerGram);

        log.info("单件编码: {} - 优惠差额计算完成：基本工费优惠={}, 附加克工费优惠={}, 附加件工费优惠={}, 基本工费每克优惠={}, 克工费每克优惠={}",
                singleCode, baseLaborCostDiscount, additionalLaborCostDiscount, pieceLaborCostDiscount, baseLaborCostDiscountPerGram, gramLaborCostDiscountPerGram);
    }

    /**
     * 获取商品信息 - 统一处理单件编码和直接传入工费信息的情况
     */
    private ProductInfo getProductInfo(CalDiscountSkuReq skuReq, MobileCustomerFeeDiscountResp discountResp) {
        try {
            String singleCode = skuReq.getSingleCode();
            Long skuId;
            BigDecimal baseLaborCosts;
            BigDecimal additionalLaborCosts;
            BigDecimal pieceLaborCosts;
            BigDecimal netWeight;
            Integer quantity;

            if (CommoditySaleModeEnum.SPOT.getCode().equals(skuReq.getSaleMode())) {
                // 场景1：通过单件编码获取商品信息
                FreightSpaceSingleProductResp singleProduct = commodityFeign.getSingleProductByCode(singleCode).getData();
                if (singleProduct == null) {
                    log.warn("单件商品不存在，单件编码：{}", singleCode);
                    return null;
                }

                // 提取公共工费信息
                skuId = singleProduct.getSkuId();
                baseLaborCosts = singleProduct.getBaseLaborCosts();
                additionalLaborCosts = singleProduct.getAdditionalLaborCosts();
                pieceLaborCosts = singleProduct.getPieceLaborCosts();
                netWeight = singleProduct.getNetWeight();
                quantity = 1; // 单件商品默认件数为1

                log.info("通过单件编码获取商品信息，singleCode：{}，skuId：{}，基本工费：{}，附加工费：{}，件工费：{}，金重：{}", singleCode, skuId, baseLaborCosts, additionalLaborCosts, pieceLaborCosts, netWeight);
            } else {
                // 场景2：通过skuId和传入的工费信息计算
                skuId = skuReq.getSkuId();
                if (skuId == null) {
                    log.warn("skuId和singleCode都为空，无法获取商品信息");
                    return null;
                }

                // 提取公共工费信息
                baseLaborCosts = skuReq.getBaseLaborCosts();
                additionalLaborCosts = skuReq.getAdditionalLaborCosts();
                pieceLaborCosts = skuReq.getPieceLaborCosts();
                netWeight = skuReq.getNetWeight();
                quantity = skuReq.getQuantity();

                log.info("使用传入的工费信息进行计算，skuId：{}，基本工费：{}，附加工费：{}，件工费：{}，金重：{}，件数：{}", skuId, baseLaborCosts, additionalLaborCosts, pieceLaborCosts, netWeight, quantity);
            }

            // 设置响应对象中的基础信息
            discountResp.setSkuId(skuId);
            discountResp.setBaseLaborCosts(baseLaborCosts != null ? baseLaborCosts : BigDecimal.ZERO);
            discountResp.setAdditionalLaborCosts(additionalLaborCosts != null ? additionalLaborCosts : BigDecimal.ZERO);
            discountResp.setPieceLaborCosts(pieceLaborCosts != null ? pieceLaborCosts : BigDecimal.ZERO);

            // 获取商品SKU信息
            CommoditySkuResp commoditySkuResp = commodityFeign.getSkuById(skuId).getData();
            if (commoditySkuResp == null) {
                log.warn("商品SKU不存在，单件编码：{}，skuId：{}", singleCode, skuId);
                return null;
            }

            // 获取商品附加参数
            List<CommodityExtraDataParamResp> dataParamList = commoditySkuResp.getDataParamList();
            if (CollectionUtils.isEmpty(dataParamList)) {
                log.warn("商品附加参数为空，商品ID：{}", commoditySkuResp.getId());
                return null;
            }

            CommodityExtraDataParamResp object = dataParamList.get(0);
            log.info("商品附加参数：{}", JSONObject.toJSONString(object));

            // 创建统一的ProductInfo对象
            return new ProductInfo(skuId, commoditySkuResp, object, baseLaborCosts, additionalLaborCosts, pieceLaborCosts, netWeight, quantity);
        } catch (Exception e) {
            log.error("获取商品信息失败，单件编码：{}，skuId：{}，错误信息：{}", skuReq.getSingleCode(), skuReq.getSkuId(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 计算基本工费优惠
     * 公式：基本工费 = 原基本工费 - 基本工费优惠
     */
    private void calculateBaseLaborCostDiscount(MobileCustomerFeeDiscountResp discountResp, ProductInfo productInfo, CustomerProcessFeeDiscountDO customerDiscount) {
        BigDecimal baseLaborCosts = productInfo.baseLaborCosts;

        // 使用抽取的通用方法获取成色信息
        String materielName = getProductFinenessFromProductInfo(productInfo);
        if (!StringUtils.hasText(materielName)) {
            log.info("商品成色属性为空，使用默认基本工费优惠");
            discountResp.setBaseLaborCosts(baseLaborCosts);
            return;
        }

        log.info("商品成色：{}", materielName);

        BigDecimal baseDiscount = BigDecimal.ZERO;
        if (materielName.contains("99999")) {
            baseDiscount = customerDiscount.getBaseDiscountFiveNine();
        } else if (materielName.contains("9999")) {
            baseDiscount = customerDiscount.getBaseDiscountTenThousand();
        } else if (materielName.contains("999")) {
            baseDiscount = customerDiscount.getBaseDiscountThousand();
        }

        discountResp.setBaseLaborCostDiscount(baseDiscount);
        BigDecimal subtract = baseLaborCosts.subtract(baseDiscount);
        discountResp.setBaseLaborCosts(ensureNonNegative(subtract));
        log.info("基本工费优惠计算完成，成色：{}，优惠金额：{}", materielName, discountResp.getBaseLaborCosts());
    }

    /**
     * 计算附加工费优惠（克工费和件工费）
     */
    private void calculateAdditionalLaborCostDiscount(MobileCustomerFeeDiscountResp discountResp, ProductInfo productInfo, CustomerProcessFeeDiscountDO customerDiscount) {
        LocalDateTime now = LocalDateTime.now();

        // 优先检查商品特定优惠（附体明细）
        CustomerProcessFeeDiscountFtDO specificDiscount = findSpecificProductDiscountWithFineness(customerDiscount, productInfo, now);

        if (specificDiscount != null) {
            calculateSpecificProductDiscount(discountResp, productInfo, specificDiscount);
        } else {
            // 使用分类优惠（表体明细）
            calculateCategoryDiscount(discountResp, productInfo, customerDiscount, now);
        }
    }


    /**
     * 查找商品特定优惠 - 带成色信息的版本
     */
    private CustomerProcessFeeDiscountFtDO findSpecificProductDiscountWithFineness(CustomerProcessFeeDiscountDO customerDiscount,
                                                                                   ProductInfo productInfo, LocalDateTime now) {
        if (CollectionUtils.isEmpty(customerDiscount.getFtContent())) {
            return null;
        }

        String itemCode = productInfo.extraDataParam.getCode();
        String fineness = getProductFinenessFromProductInfo(productInfo);

        log.info("开始查找商品特定优惠，itemCode: {}, 成色: {}", itemCode, fineness);

        // 过滤未过期的优惠配置
        List<CustomerProcessFeeDiscountFtDO> validDiscounts = customerDiscount.getFtContent().stream()
                .filter(ft -> ft.getDiscountEndTime() == null || ft.getDiscountEndTime().isAfter(now))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(validDiscounts)) {
            log.info("没有找到有效的特定优惠配置");
            return null;
        }

        // 按优先级查找匹配的优惠配置
        CustomerProcessFeeDiscountFtDO bestMatch = findBestMatchDiscount(validDiscounts, itemCode, fineness, productInfo);

        if (bestMatch != null) {
            log.info("找到最佳匹配的特定优惠配置: {}", JSONObject.toJSONString(bestMatch));
        } else {
            log.info("未找到匹配的特定优惠配置");
        }

        return bestMatch;
    }

    /**
     * 从ProductInfo中获取商品的成色信息
     */
    private String getProductFinenessFromProductInfo(ProductInfo productInfo) {
        return getProductAttributeValue(productInfo, "成色");
    }

    /**
     * 从ProductInfo中获取指定的商品属性值
     *
     * @param productInfo   商品信息
     * @param attributeName 属性名称
     * @return 属性值，如果不存在则返回null
     */
    private String getProductAttributeValue(ProductInfo productInfo, String attributeName) {
        try {
            if (productInfo == null || productInfo.commoditySkuResp == null) {
                log.debug("商品信息或SKU信息为空");
                return null;
            }

            Map<String, String> skuAttributeMap = productInfo.commoditySkuResp.getSkuAttributeMap();
            if (skuAttributeMap == null || skuAttributeMap.isEmpty()) {
                log.debug("SKU属性映射为空");
                return null;
            }

            String attributeValue = skuAttributeMap.get(attributeName);
            log.info("从SKU属性中获取{}信息: {}", attributeName, attributeValue);
            return attributeValue;
        } catch (Exception e) {
            log.warn("获取商品属性{}失败，错误: {}", attributeName, e.getMessage());
            return null;
        }
    }

    /**
     * 按优先级查找最佳匹配的优惠配置
     * 优先级：3个条件都符合 > 2个条件符合 > 1个条件符合（第三方参数）
     */
    private CustomerProcessFeeDiscountFtDO findBestMatchDiscount(List<CustomerProcessFeeDiscountFtDO> validDiscounts,
                                                                 String itemCode, String fineness, ProductInfo productInfo) {

        // 分别收集不同匹配度的优惠配置
        List<CustomerProcessFeeDiscountFtDO> threeMatches = new ArrayList<>();  // 3个条件都符合
        List<CustomerProcessFeeDiscountFtDO> twoMatches = new ArrayList<>();    // 2个条件符合
        List<CustomerProcessFeeDiscountFtDO> oneMatches = new ArrayList<>();    // 1个条件符合（第三方参数）

        for (CustomerProcessFeeDiscountFtDO discount : validDiscounts) {
            int matchCount = calculateMatchCount(discount, itemCode, fineness, productInfo);

            if (matchCount == 3) {
                threeMatches.add(discount);
            } else if (matchCount == 2) {
                twoMatches.add(discount);
            } else if (matchCount == 1 && isItemCodeMatch(discount, itemCode)) {
                // 只有第三方参数匹配的情况
                oneMatches.add(discount);
            }
        }

        // 按优先级返回第一个匹配的配置
        if (!threeMatches.isEmpty()) {
            log.info("找到3个条件都符合的优惠配置，数量: {}", threeMatches.size());
            return threeMatches.get(0);
        } else if (!twoMatches.isEmpty()) {
            log.info("找到2个条件符合的优惠配置，数量: {}", twoMatches.size());
            return twoMatches.get(0);
        } else if (!oneMatches.isEmpty()) {
            log.info("找到1个条件符合的优惠配置（第三方参数），数量: {}", oneMatches.size());
            return oneMatches.get(0);
        }

        return null;
    }

    /**
     * 计算匹配条件的数量
     */
    private int calculateMatchCount(CustomerProcessFeeDiscountFtDO discount, String itemCode, String fineness, ProductInfo productInfo) {
        int matchCount = 0;

        // 条件1：材质名称 = 中台&商城【成色】
        if (isMaterialNameMatch(discount, fineness)) {
            matchCount++;
        }

        // 条件2：是否无基本工费匹配（商品基本工费为0 且 规则配置为"是"）
        if (isNoBaseFeeMatch(discount, productInfo)) {
            matchCount++;
        }

        // 条件3：第三方参数（itemCode）匹配
        if (isItemCodeMatch(discount, itemCode)) {
            matchCount++;
        }

        log.debug("优惠配置匹配度计算 - ID: {}, 材质名称: {}, 是否无基本工费: {}, 第三方参数: {}, 匹配数: {}",
                discount.getId(), discount.getMaterialName(), discount.getIsNoBaseFee(),
                discount.getItemCode(), matchCount);

        return matchCount;
    }

    /**
     * 判断材质名称是否匹配成色
     */
    private boolean isMaterialNameMatch(CustomerProcessFeeDiscountFtDO discount, String fineness) {
        if (discount.getMaterialName() == null || fineness == null) {
            return false;
        }
        return discount.getMaterialName().equals(fineness);
    }

    /**
     * 判断是否无基本工费匹配
     * 匹配条件：商品基本工费为0 且 规则配置为"是"
     */
    private boolean isNoBaseFeeMatch(CustomerProcessFeeDiscountFtDO discount, ProductInfo productInfo) {
        // 检查规则配置是否为"是"
        String isNoBaseFee = discount.getIsNoBaseFee();
        boolean ruleConfigured = "是".equals(isNoBaseFee);

        // 检查商品基本工费是否为0
        boolean productHasNoBaseFee = false;
        if (productInfo != null) {
            productHasNoBaseFee = productInfo.baseLaborCosts.compareTo(BigDecimal.ZERO) == 0;
        }

        // 两个条件都满足才算匹配
        boolean isMatch = ruleConfigured && productHasNoBaseFee;

        log.info("无基本工费匹配判断 - 规则配置: {}, 商品基本工费: {}, 是否匹配: {}",
                isNoBaseFee, productInfo != null ? productInfo.baseLaborCosts : "null", isMatch);

        return isMatch;
    }

    /**
     * 判断第三方参数（itemCode）是否匹配
     */
    private boolean isItemCodeMatch(CustomerProcessFeeDiscountFtDO discount, String itemCode) {
        if (discount.getItemCode() == null || itemCode == null) {
            return false;
        }
        return discount.getItemCode().equals(itemCode);
    }

    /**
     * 计算商品特定优惠
     */
    private void calculateSpecificProductDiscount(MobileCustomerFeeDiscountResp discountResp, ProductInfo productInfo, CustomerProcessFeeDiscountFtDO specificDiscount) {
        log.info("找到商品特定优惠：{}", JSONObject.toJSONString(specificDiscount));
        BigDecimal additionalLaborCosts = productInfo.additionalLaborCosts;

        // 附加克工费计算：如果有指定克工费，直接使用；否则使用克工费优惠
        BigDecimal specifiedGramFee = specificDiscount.getSpecifiedGramFee();
        if (specifiedGramFee != null && specifiedGramFee.compareTo(BigDecimal.ZERO) > 0) {
            // 附加克工费 = 指定克工费（克重优惠）
            discountResp.setAdditionalLaborCosts(specifiedGramFee);
            // 设置指定克工费字段
            discountResp.setSpecifiedGramLaborCost(specifiedGramFee);
            // 计算指定克工费优惠金额 = 原始附加克工费 - 指定克工费
            BigDecimal specifiedGramLaborCostDiscount = additionalLaborCosts.subtract(specifiedGramFee);
            discountResp.setSpecifiedGramLaborCostDiscount(ensureNonNegative(specifiedGramLaborCostDiscount));
            // 设置优惠类型为克重优惠
            setGramLaborDiscountType(discountResp, GramLaborDiscountTypeEnum.WEIGHT_REDUCTION);

            log.info("使用指定克工费：原始附加克工费={}, 指定克工费={}, 指定克工费优惠金额={}",
                    additionalLaborCosts, specifiedGramFee, specifiedGramLaborCostDiscount);
        } else {
            // 使用克工费优惠（折扣优惠）
            BigDecimal gramFeeDiscount = specificDiscount.getGramFeeDiscount();
            gramFeeDiscount = gramFeeDiscount != null ? gramFeeDiscount : BigDecimal.ZERO;
            discountResp.setAdditionalLaborCosts(additionalLaborCosts.subtract(gramFeeDiscount));
            // 指定克工费相关字段设置为零（因为没有使用指定克工费）
            discountResp.setSpecifiedGramLaborCost(BigDecimal.ZERO);
            discountResp.setSpecifiedGramLaborCostDiscount(BigDecimal.ZERO);
            // 设置优惠类型为折扣优惠
            setGramLaborDiscountType(discountResp, GramLaborDiscountTypeEnum.DISCOUNT);

            log.info("使用克工费优惠：原始附加克工费={}, 克工费优惠={}, 优惠后附加克工费={}",
                    additionalLaborCosts, gramFeeDiscount, discountResp.getAdditionalLaborCosts());
        }

        discountResp.setPieceLaborCosts(productInfo.pieceLaborCosts);

        log.info("商品特定优惠计算完成，最终克工费优惠：{}，优惠类型：{}",
                discountResp.getAdditionalLaborCosts(), discountResp.getGramLaborDiscountTypeName());
    }

    /**
     * 计算分类优惠
     */
    private void calculateCategoryDiscount(MobileCustomerFeeDiscountResp discountResp, ProductInfo productInfo, CustomerProcessFeeDiscountDO customerDiscount, LocalDateTime now) {
        if (CollectionUtils.isEmpty(customerDiscount.getBtContent())) {
            log.info("没有找到分类优惠配置");
            return;
        }

        String itemCategory = productInfo.extraDataParam.getType();
        List<CustomerProcessFeeDiscountBtDO> categoryDiscounts = customerDiscount.getBtContent().stream().filter(bt -> itemCategory.equals(bt.getItemCategory())).filter(bt -> bt.getDiscountEndTime() == null || bt.getDiscountEndTime().isAfter(now)).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(categoryDiscounts)) {
            log.info("没有找到分类优惠：{}", itemCategory);
            return;
        }

        for (CustomerProcessFeeDiscountBtDO categoryDiscount : categoryDiscounts) {
            BigDecimal netWeight = productInfo.netWeight;
            BigDecimal startGramFee = categoryDiscount.getStartGramFee();
            BigDecimal endGramFee = categoryDiscount.getEndGramFee();

            if (startGramFee != null && endGramFee != null && netWeight.compareTo(startGramFee) >= 0 && netWeight.compareTo(endGramFee) < 0) {
                log.info("找到分类优惠：{}", JSONObject.toJSONString(categoryDiscount));
                // 计算克工费优惠
                calculateGramFeeDiscount(discountResp, productInfo, categoryDiscount);

                // 计算件工费优惠
                calculatePieceFeeDiscount(discountResp, productInfo, categoryDiscount);
            }
        }
    }

    /**
     * 计算克工费优惠
     * 公式：附加克工费 = (原附加克工费 - 克工费优惠) × 克工费折扣 - 指定克工费优惠
     */
    private void calculateGramFeeDiscount(MobileCustomerFeeDiscountResp discountResp, ProductInfo productInfo, CustomerProcessFeeDiscountBtDO categoryDiscount) {
        BigDecimal netWeight = productInfo.netWeight;

        BigDecimal additionalLaborCosts = productInfo.additionalLaborCosts;

        BigDecimal startGramFee = categoryDiscount.getStartGramFee();
        BigDecimal endGramFee = categoryDiscount.getEndGramFee();

        if (startGramFee != null && endGramFee != null && netWeight.compareTo(startGramFee) >= 0 && netWeight.compareTo(endGramFee) < 0) {

            BigDecimal gramFeeDiscount = categoryDiscount.getGramFeeDiscount();
            BigDecimal gramFeeRate = categoryDiscount.getGramFeeRate();

            BigDecimal finalDiscount = BigDecimal.ZERO;
            if (gramFeeDiscount != null && gramFeeDiscount.compareTo(BigDecimal.ZERO) > 0) {
                // 直接使用克工费优惠金额，确保不为负数（克重优惠）
                finalDiscount = ensureNonNegative(additionalLaborCosts.subtract(gramFeeDiscount));
                // 设置优惠类型为克重优惠
                setGramLaborDiscountType(discountResp, GramLaborDiscountTypeEnum.WEIGHT_REDUCTION);
            } else if (gramFeeRate != null && gramFeeRate.compareTo(BigDecimal.ZERO) > 0) {
                // 使用克工费折扣率计算：原附加克工费 × 折扣率（折扣优惠）
                finalDiscount = ensureNonNegative(additionalLaborCosts.multiply(gramFeeRate));
                // 设置优惠类型为折扣优惠
                setGramLaborDiscountType(discountResp, GramLaborDiscountTypeEnum.DISCOUNT);
            }

            discountResp.setAdditionalLaborCosts(finalDiscount);

            // 分类优惠不使用指定克工费，将相关字段设置为零
            discountResp.setSpecifiedGramLaborCost(BigDecimal.ZERO);
            discountResp.setSpecifiedGramLaborCostDiscount(BigDecimal.ZERO);

            log.info("克工费优惠计算完成，实际重量：{}，最终优惠金额：{}，优惠类型：{}",
                    netWeight, finalDiscount, discountResp.getGramLaborDiscountTypeName());
        }
    }

    /**
     * 计算件工费优惠
     * 公式：附加件工费 = (原附加件工费 - 件工费优惠) × 件工费折扣
     */
    private void calculatePieceFeeDiscount(MobileCustomerFeeDiscountResp discountResp, ProductInfo productInfo, CustomerProcessFeeDiscountBtDO categoryDiscount) {
        BigDecimal pieceLaborCosts = productInfo.pieceLaborCosts;

        BigDecimal startPieceFee = categoryDiscount.getStartPieceFee();
        BigDecimal endPieceFee = categoryDiscount.getEndPieceFee();

        if (startPieceFee != null && endPieceFee != null && pieceLaborCosts.compareTo(startPieceFee) >= 0 && pieceLaborCosts.compareTo(endPieceFee) < 0) {

            BigDecimal pieceFeeDiscount = categoryDiscount.getPieceFeeDiscount();
            BigDecimal pieceFeeRate = categoryDiscount.getPieceFeeRate();

            BigDecimal finalDiscount = BigDecimal.ZERO;
            if (pieceFeeDiscount != null && pieceFeeDiscount.compareTo(BigDecimal.ZERO) > 0) {
                // 直接使用件工费优惠金额，确保不为负数（件数优惠）
                finalDiscount = ensureNonNegative(pieceLaborCosts.subtract(pieceFeeDiscount));
                // 设置优惠类型为件数优惠
                setPieceLaborDiscountType(discountResp, PieceLaborDiscountTypeEnum.PIECE_REDUCTION);
            } else if (pieceFeeRate != null && pieceFeeRate.compareTo(BigDecimal.ZERO) > 0) {
                // 使用件工费折扣率计算：原件工费 × 折扣率（折扣优惠）
                finalDiscount = ensureNonNegative(pieceLaborCosts.multiply(pieceFeeRate));
                // 设置优惠类型为折扣优惠
                setPieceLaborDiscountType(discountResp, PieceLaborDiscountTypeEnum.DISCOUNT);
            }

            discountResp.setPieceLaborCosts(finalDiscount);
            log.info("件工费优惠计算完成，原件工费：{}，最终优惠金额：{}，优惠类型：{}",
                    pieceLaborCosts, finalDiscount, discountResp.getPieceLaborDiscountTypeName());
        }
    }

    /**
     * 计算最终金额（包括原始金额、优惠后金额和优惠差额）
     * 1. 基础工费金额 = 基本工费 × 金重
     * 2. 件工费金额 = 附加件工费 × 件数
     * 3. 克工费金额 = 基本工费 × 金重 + 附加克工费 × 金重
     * 4. 附加工费金额 = 附加克工费 × 金重 + 附加件工费 × 件数
     */
    private void calculateFinalAmounts(MobileCustomerFeeDiscountResp discountResp, ProductInfo productInfo) {
        // 获取金重（使用netWeight），件数默认为1
        BigDecimal netWeight = productInfo.netWeight;
        netWeight = netWeight != null ? netWeight : BigDecimal.ZERO;
        BigDecimal pieceCount = BigDecimal.ONE; // 件数默认为1

        // === 计算原始金额 ===

        // 1. 原始基础工费金额 = 基本工费 × 金重
        BigDecimal originalBaseLaborCostAmount = discountResp.getOriginalBaseLaborCost().multiply(netWeight);

        // 2. 原始件工费金额 = 附加件工费 × 件数
        BigDecimal originalPieceLaborCostAmount = discountResp.getOriginalPieceLaborCost().multiply(pieceCount);

        // 3. 原始克工费金额 = 基本工费 × 金重 + 附加克工费 × 金重
        BigDecimal originalGramLaborCostAmount = originalBaseLaborCostAmount.add(discountResp.getOriginalAdditionalLaborCost().multiply(netWeight));

        // 4. 原始附加工费金额 = 附加克工费 × 金重 + 附加件工费 × 件数
        BigDecimal originalAdditionalLaborCostAmount = discountResp.getOriginalAdditionalLaborCost().multiply(netWeight).add(originalPieceLaborCostAmount);

        discountResp.setOriginalBaseLaborCostAmount(ensureNonNegative(originalBaseLaborCostAmount));
        discountResp.setOriginalPieceLaborCostAmount(ensureNonNegative(originalPieceLaborCostAmount));
        discountResp.setOriginalGramLaborCostAmount(ensureNonNegative(originalGramLaborCostAmount));
        discountResp.setOriginalAdditionalLaborCostAmount(ensureNonNegative(originalAdditionalLaborCostAmount));

        // === 计算优惠后金额 ===

        // 获取优惠后的单价
        BigDecimal baseLaborCost = discountResp.getBaseLaborCosts() != null ? discountResp.getBaseLaborCosts() : BigDecimal.ZERO;
        BigDecimal additionalLaborCost = discountResp.getAdditionalLaborCosts() != null ? discountResp.getAdditionalLaborCosts() : BigDecimal.ZERO;
        BigDecimal pieceLaborCost = discountResp.getPieceLaborCosts() != null ? discountResp.getPieceLaborCosts() : BigDecimal.ZERO;

        // 1. 优惠后基础工费金额 = 优惠后基本工费 × 金重
        BigDecimal baseLaborCostAmount = ensureNonNegative(baseLaborCost.multiply(netWeight));

        // 2. 优惠后件工费金额 = 优惠后附加件工费 × 件数
        BigDecimal pieceLaborCostAmount = ensureNonNegative(pieceLaborCost.multiply(pieceCount));

        // 3. 优惠后克工费金额 = 优惠后基本工费 × 金重 + 优惠后附加克工费 × 金重
        BigDecimal gramLaborCostAmount = ensureNonNegative(baseLaborCost.multiply(netWeight).add(additionalLaborCost.multiply(netWeight)));

        // 4. 优惠后附加工费金额 = 优惠后附加克工费 × 金重 + 优惠后附加件工费 × 件数
        BigDecimal additionalLaborCostAmount = ensureNonNegative(additionalLaborCost.multiply(netWeight).add(pieceLaborCostAmount));

        // 设置优惠后金额到现有字段（保持向后兼容）
        discountResp.setBaseLaborCosts(baseLaborCostAmount);
        discountResp.setAdditionalLaborCosts(gramLaborCostAmount);
        discountResp.setPieceLaborCosts(pieceLaborCostAmount);
        discountResp.setAdditionalLaborCostAmount(additionalLaborCostAmount);

        // === 计算优惠金额差额 ===

        BigDecimal baseLaborCostDiscountAmount = originalBaseLaborCostAmount.subtract(baseLaborCostAmount);
        BigDecimal pieceLaborCostDiscountAmount = originalPieceLaborCostAmount.subtract(pieceLaborCostAmount);
        BigDecimal gramLaborCostDiscountAmount = originalGramLaborCostAmount.subtract(gramLaborCostAmount);
        BigDecimal additionalLaborCostDiscountAmount = originalAdditionalLaborCostAmount.subtract(additionalLaborCostAmount);

        discountResp.setBaseLaborCostDiscountAmount(ensureNonNegative(baseLaborCostDiscountAmount));
        discountResp.setPieceLaborCostDiscountAmount(ensureNonNegative(pieceLaborCostDiscountAmount));
        discountResp.setGramLaborCostDiscountAmount(ensureNonNegative(gramLaborCostDiscountAmount));
        discountResp.setAdditionalLaborCostDiscountAmount(ensureNonNegative(additionalLaborCostDiscountAmount));

        log.info("工费金额计算完成 - 实际重量：{}，件数：{}", netWeight, pieceCount);
        log.info("原始金额 - 基础工费：{}，克工费：{}，件工费：{}，附加工费：{}", originalBaseLaborCostAmount, originalGramLaborCostAmount, originalPieceLaborCostAmount, originalAdditionalLaborCostAmount);
        log.info("优惠后金额 - 基础工费：{}，克工费：{}，件工费：{}，附加工费：{}", baseLaborCostAmount, gramLaborCostAmount, pieceLaborCostAmount, additionalLaborCostAmount);
        log.info("优惠差额 - 基础工费：{}，克工费：{}，件工费：{}，附加工费：{}", baseLaborCostDiscountAmount, gramLaborCostDiscountAmount, pieceLaborCostDiscountAmount, additionalLaborCostDiscountAmount);
    }

    /**
     * 确保金额不为负数的工具方法
     *
     * @param amount 原始金额
     * @return 如果原始金额为负数则返回0，否则返回原始金额
     */
    private BigDecimal ensureNonNegative(BigDecimal amount) {
        return amount != null && amount.compareTo(BigDecimal.ZERO) >= 0 ? amount : BigDecimal.ZERO;
    }

    /**
     * 设置克工费优惠类型
     * @param discountResp 响应对象
     * @param discountType 优惠类型枚举
     */
    private void setGramLaborDiscountType(MobileCustomerFeeDiscountResp discountResp, GramLaborDiscountTypeEnum discountType) {
        if (discountType != null) {
            discountResp.setGramLaborDiscountType(discountType.getCode());
            discountResp.setGramLaborDiscountTypeName(discountType.getName());
            log.debug("设置克工费优惠类型：{}({})", discountType.getName(), discountType.getCode());
        }
    }

    /**
     * 设置件工费优惠类型
     * @param discountResp 响应对象
     * @param discountType 优惠类型枚举
     */
    private void setPieceLaborDiscountType(MobileCustomerFeeDiscountResp discountResp, PieceLaborDiscountTypeEnum discountType) {
        if (discountType != null) {
            discountResp.setPieceLaborDiscountType(discountType.getCode());
            discountResp.setPieceLaborDiscountTypeName(discountType.getName());
            log.debug("设置件工费优惠类型：{}({})", discountType.getName(), discountType.getCode());
        }
    }

    /**
     * 确保BigDecimal不为null的工具方法
     *
     * @param value 原始值
     * @return 如果为null则返回0，否则返回原始值
     */
    private BigDecimal ensureNonNull(BigDecimal value) {
        return value != null ? value : BigDecimal.ZERO;
    }


    /**
     * 商品信息封装类 - 统一处理单件商品和直接传入工费信息的情况
     */
    private static class ProductInfo {
        // 商品基础信息
        final Long skuId;
        final CommoditySkuResp commoditySkuResp;
        final CommodityExtraDataParamResp extraDataParam;

        // 工费信息（从单件商品或直接传入中提取的公共信息）
        final BigDecimal baseLaborCosts;
        final BigDecimal additionalLaborCosts;
        final BigDecimal pieceLaborCosts;
        final BigDecimal netWeight;
        final Integer quantity;

        ProductInfo(Long skuId, CommoditySkuResp commoditySkuResp, CommodityExtraDataParamResp extraDataParam, BigDecimal baseLaborCosts, BigDecimal additionalLaborCosts, BigDecimal pieceLaborCosts, BigDecimal netWeight, Integer quantity) {
            this.skuId = skuId;
            this.commoditySkuResp = commoditySkuResp;
            this.extraDataParam = extraDataParam;
            this.baseLaborCosts = baseLaborCosts != null ? baseLaborCosts : BigDecimal.ZERO;
            this.additionalLaborCosts = additionalLaborCosts != null ? additionalLaborCosts : BigDecimal.ZERO;
            this.pieceLaborCosts = pieceLaborCosts != null ? pieceLaborCosts : BigDecimal.ZERO;
            this.netWeight = netWeight != null ? netWeight : BigDecimal.ZERO;
            this.quantity = quantity != null ? quantity : 1;
        }
    }


}
