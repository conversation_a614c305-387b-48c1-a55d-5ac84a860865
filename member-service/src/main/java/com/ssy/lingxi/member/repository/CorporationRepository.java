package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.basic.CorporationDO;
import com.ssy.lingxi.member.model.resp.basic.CorporationInfoResp;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * 实控人repository
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since 2025/6/7
 */
public interface CorporationRepository extends JpaRepository<CorporationDO, Long>, JpaSpecificationExecutor<CorporationDO> {


    CorporationDO findFirstByMemberId(Long memberId);

    CorporationDO findFirstByCode(String code);

    List<CorporationDO> findAllByCodeIn(List<String> codes);

    List<CorporationDO> findAllByActualControllerId(Long actualControllerId);

    List<CorporationDO> findAllByActualControllerIdIn(List<Long> actualControllerIds);

    List<CorporationDO> findAllByGroupIdentifier(String groupIdentifier);

    boolean existsByName(String name);

    boolean existsByUnifiedSocialCode(String unifiedSocialCode);

    boolean existsByNameAndIdNot(String name, Long id);

    List<CorporationDO> findAllByMemberIdIn(List<Long> memberIds);

    @Query(value = "select new com.ssy.lingxi.member.model.resp.basic.CorporationInfoResp(c.actualControllerId,c.groupIdentifier,c.name,m.id) from MemberDO m inner join CorporationDO c on m.corporationId=c.id where m.id in (:memberIds)")
    List<CorporationInfoResp> findByMemberIds(List<Long> memberIds);
}
