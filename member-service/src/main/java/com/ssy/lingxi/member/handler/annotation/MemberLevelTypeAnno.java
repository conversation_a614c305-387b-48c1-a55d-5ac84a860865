package com.ssy.lingxi.member.handler.annotation;

import com.ssy.lingxi.member.handler.validator.MemberLevelTypeValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 会员等级类型校验注解
 */
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE, ElementType.CONSTRUCTOR, ElementType.PARAMETER})
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = MemberLevelTypeValidator.class)
public @interface MemberLevelTypeAnno {
    boolean required() default true;

    String message() default "会员等级类型参数值不在定义范围内";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
