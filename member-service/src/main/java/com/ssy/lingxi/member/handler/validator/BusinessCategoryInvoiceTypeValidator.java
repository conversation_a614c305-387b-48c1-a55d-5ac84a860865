package com.ssy.lingxi.member.handler.validator;

import com.ssy.lingxi.component.base.enums.member.BusinessCategoryInvoiceTypeEnum;
import com.ssy.lingxi.member.handler.annotation.BusinessCategoryInvoiceTypeAnnotation;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Arrays;

/**
 * 会员入库分类 - 发票类型枚举校验注解验证类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-12-16
 */
public class BusinessCategoryInvoiceTypeValidator implements ConstraintValidator<BusinessCategoryInvoiceTypeAnnotation, Integer> {
    @Override
    public boolean isValid(Integer value, ConstraintValidatorContext context) {
        if(value == null) {
            return false;
        }

        return Arrays.stream(BusinessCategoryInvoiceTypeEnum.values()).map(BusinessCategoryInvoiceTypeEnum::getCode).anyMatch(v -> v.equals(value));
    }
}
