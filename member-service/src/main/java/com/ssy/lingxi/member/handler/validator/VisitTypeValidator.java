package com.ssy.lingxi.member.handler.validator;

import com.ssy.lingxi.member.enums.VisitTypeEnum;
import com.ssy.lingxi.member.handler.annotation.VisitTypeAnnotation;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Arrays;

/**
 * 拜访类型参数校验注解校验类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-31
 */
public class VisitTypeValidator implements ConstraintValidator<VisitTypeAnnotation, Integer> {
    @Override
    public boolean isValid(Integer value, ConstraintValidatorContext context) {
        if(value == null || value <= 0) {
            return false;
        }

        return Arrays.stream(VisitTypeEnum.values()).anyMatch(v -> v.getCode().equals(value));
    }
}
