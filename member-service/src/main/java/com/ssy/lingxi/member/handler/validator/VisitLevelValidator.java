package com.ssy.lingxi.member.handler.validator;

import com.ssy.lingxi.member.enums.VisitLevelEnum;
import com.ssy.lingxi.member.handler.annotation.VisitLevelAnnotation;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Arrays;

/**
 * 拜访级别参数校验注解校验类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-31
 */
public class VisitLevelValidator implements ConstraintValidator<VisitLevelAnnotation, Integer> {
    @Override
    public boolean isValid(Integer value, ConstraintValidatorContext context) {
        if(value == null || value <= 0) {
            return false;
        }

        return Arrays.stream(VisitLevelEnum.values()).anyMatch(v -> v.getCode().equals(value));
    }
}
