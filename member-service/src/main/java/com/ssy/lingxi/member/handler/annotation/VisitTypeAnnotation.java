package com.ssy.lingxi.member.handler.annotation;

import com.ssy.lingxi.member.handler.validator.VisitTypeValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 拜访类型参数校验注解
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-31
 */
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = {VisitTypeValidator.class})
public @interface VisitTypeAnnotation {
    String message() default "拜访类型参数值不在定义范围内";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
