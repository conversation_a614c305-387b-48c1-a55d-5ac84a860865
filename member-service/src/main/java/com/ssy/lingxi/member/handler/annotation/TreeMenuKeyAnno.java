package com.ssy.lingxi.member.handler.annotation;

import com.ssy.lingxi.member.handler.validator.TreeMenuKeyValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 树形菜单key值校验处理类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-06-16
 */
@Target({ElementType.TYPE, ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = TreeMenuKeyValidator.class)
public @interface TreeMenuKeyAnno {
    boolean required() default true;

    String message() default "菜单key格式错误";

    Class<?>[] groups() default { };

    Class<? extends Payload>[] payload() default { };
}
