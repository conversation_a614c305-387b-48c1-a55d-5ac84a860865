package com.ssy.lingxi.member.serviceImpl.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ssy.lingxi.aftersales.api.feign.IAfterCommonFeign;
import com.ssy.lingxi.aftersales.api.model.req.MemberIdReq;
import com.ssy.lingxi.commodity.api.feign.ICountryAreaFeign;
import com.ssy.lingxi.commodity.api.model.resp.support.CountryAreaResp;
import com.ssy.lingxi.common.constant.Constant;
import com.ssy.lingxi.common.constant.RedisConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.util.DateTimeUtil;
import com.ssy.lingxi.component.base.config.SmsConfig;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.manage.ContentNoticeTypeEnum;
import com.ssy.lingxi.component.base.enums.member.*;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.BusinessAssertUtil;
import com.ssy.lingxi.component.base.util.PasswordUtil;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.component.redis.service.IRedisUtils;
import com.ssy.lingxi.member.api.enums.BaiDuConfigEnum;
import com.ssy.lingxi.member.api.model.resp.MemberBrandInfoResp;
import com.ssy.lingxi.member.config.MemberRefreshConfig;
import com.ssy.lingxi.member.constant.MemberRedisConstant;
import com.ssy.lingxi.member.entity.do_.RealNameConfigDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberCancellationDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.entity.do_.basic.UserDO;
import com.ssy.lingxi.member.entity.do_.branch.MemberBranchDO;
import com.ssy.lingxi.member.entity.do_.complain.MemberComplaintDO;
import com.ssy.lingxi.member.enums.MemberCancellationEnum;
import com.ssy.lingxi.member.enums.MemberInnerStatusEnum;
import com.ssy.lingxi.member.enums.MemberValidateHistoryOperationEnum;
import com.ssy.lingxi.member.handler.listener.event.RedisKeyRemoveEvent;
import com.ssy.lingxi.member.model.req.basic.*;
import com.ssy.lingxi.member.model.req.login.EmailLoginSmsCode;
import com.ssy.lingxi.member.model.req.login.PhoneLoginSmsCode;
import com.ssy.lingxi.member.model.req.maintenance.*;
import com.ssy.lingxi.member.model.req.platform.PlatformManageUpdateForgetPasswordReq;
import com.ssy.lingxi.member.model.req.platform.PlatformUpdateForgetPasswordReq;
import com.ssy.lingxi.member.model.resp.maintenance.MemberCancellationFailResp;
import com.ssy.lingxi.member.model.resp.maintenance.UserAccountAuthQueryResp;
import com.ssy.lingxi.member.model.resp.maintenance.UserAccountSecurityQueryResp;
import com.ssy.lingxi.member.model.resp.platform.PlatformUpdateForgetPasswordResp;
import com.ssy.lingxi.member.repository.*;
import com.ssy.lingxi.member.service.IMemberService;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.base.IBaseMemberHistoryService;
import com.ssy.lingxi.member.service.feign.IMemberFeignService;
import com.ssy.lingxi.member.service.feign.ISmsFeignService;
import com.ssy.lingxi.member.service.web.IMemberAccountSecurityService;
import com.ssy.lingxi.member.service.web.IRealNameConfigService;
import com.ssy.lingxi.member.util.MailUtil;
import com.ssy.lingxi.member.util.NameUtil;
import com.ssy.lingxi.member.util.SecurityStringUtil;
import com.ssy.lingxi.member.util.baidu.AuthUtil;
import com.ssy.lingxi.member.util.baidu.IdCardUtil;
import com.ssy.lingxi.order.api.feign.IOrderProcessFeign;
import com.ssy.lingxi.order.api.model.req.OrderFeignMemberIdReq;
import com.ssy.lingxi.pay.api.feign.IAssetAccountFeign;
import com.ssy.lingxi.pay.api.model.req.CheckAssetAccountReq;
import com.ssy.lingxi.settlement.api.feign.IMemberSettlementFeign;
import com.ssy.lingxi.settlement.api.model.req.SettlementMemberIdReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 用户账号安全接口实现类
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-07-06
 */
@Service
@Slf4j
public class MemberAccountSecurityServiceImpl implements IMemberAccountSecurityService {
    @Resource
    private UserRepository userRepository;

    @Resource
    private MemberRepository memberRepository;

    @Resource
    private IBaseMemberCacheService memberCacheService;

    @Resource
    private ISmsFeignService smsFeignService;

    @Resource
    private IRealNameConfigService memberAuthTypeConfigFeignService;

    @Resource
    private MemberRelationRepository relationRepository;

    @Resource
    private MemberComplaintRepository memberComplaintRepository;

    @Resource
    private IAfterCommonFeign afterCommonFeignService;

    @Resource
    private IMemberSettlementFeign memberSettlementControllerFeignService;

    @Resource
    private IOrderProcessFeign orderFeignService;

    @Resource
    private MemberRefreshConfig memberRefreshConfig;

    @Resource
    private SmsConfig smsConfig;

    @Resource
    private IAssetAccountFeign assetAccountFeignService;

    @Resource
    private IBaseMemberHistoryService baseMemberHistoryService;

    @Resource
    private MemberCancellationRepository cancellationRepository;

    @Resource
    private ICountryAreaFeign countryAreaFeign;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    @Resource
    private IRedisUtils redisUtils;

    @Resource
    private MemberBranchRepository memberBranchRepository;

    /**
     * “账户安全”页面，查询用户的手机号码和邮箱，以掩码方式返回
     *
     * @param headers Http头部信息
     * @return 操作结果
     */
    @Override
    public UserAccountSecurityQueryResp getPhoneAndEmail(HttpHeaders headers) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        UserDO userDO = userRepository.findById(loginUser.getUserId()).orElse(null);
        if (userDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        }
        String branchId = headers.getFirst(Constant.BRANCH_ID);
        MemberDO memberDO = null;
        if(!StringUtils.isEmpty(branchId)){
            MemberBranchDO memberBranchDO = memberBranchRepository.findById(Long.valueOf(branchId)).orElse(null);
            if(memberBranchDO == null){
                throw new BusinessException(ResponseCodeEnum.ORDER_RELATION_BRANCH_NOT_EXIST);
            }
            memberDO = memberRepository.findById(memberBranchDO.getMemberId()).orElse(null);
            if (memberDO == null) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
            }
        }else{
            memberDO = userDO.getMember();
        }
        UserAccountSecurityQueryResp queryVO = new UserAccountSecurityQueryResp();
        queryVO.setUserId(userDO.getId());
        queryVO.setPhone(SecurityStringUtil.getMaskPhone(userDO.getTelCode(), userDO.getPhone()));
        queryVO.setEmail(SecurityStringUtil.getMaskMail(userDO.getEmail()));
        queryVO.setHasPayPassword(StringUtils.hasLength(memberDO.getPayPassword()) ? EnableDisableStatusEnum.ENABLE.getCode() : EnableDisableStatusEnum.DISABLE.getCode());
        queryVO.setIsAuth(userDO.getIsAuth());
        int i = 0;
        if (StringUtils.hasLength(memberDO.getPayPassword())) {
            i++;
        }

        if (StringUtils.hasLength(userDO.getEmail())) {
            i++;
        }

        if (StringUtils.hasLength(userDO.getPhone())) {
            i++;
        }

        if (StringUtils.hasLength(userDO.getPassword())) {
            i++;
        }

        if (userDO.getIsAuth()) {
            i++;
        }

        queryVO.setRate(i * 20);

        return queryVO;
    }

    /**
     * （通用）检查支付密码是否正确
     *
     * @param headers       Http头部信息
     * @param payPasswordReq 接口参数
     */
    @Override
    public void checkPayPassword(HttpHeaders headers, PayPasswordReq payPasswordReq) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        UserDO userDO = userRepository.findById(loginUser.getUserId()).orElse(null);
        if (userDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        }

        MemberDO memberDO = userDO.getMember();
        if (memberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        if (!StringUtils.hasLength(memberDO.getPayPassword())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_PAY_PASSWORD_NOT_SET);
        }

        if (!PasswordUtil.checkPassword(memberDO.getPayPassword(), payPasswordReq.getPayPassword())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_PAY_PASSWORD_INCORRECT);
        }
    }

    /**
     * 修改登录密码（手机校验码验证）时，发送手机短信验证码
     *
     * @param headers Http头部信息
     */
    @Override
    public void sendChangePasswordSmsCode(HttpHeaders headers) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        UserDO userDO = userRepository.findById(loginUser.getUserId()).orElse(null);
        if (userDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        }

        if (!StringUtils.hasLength(userDO.getPhone())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_PHONE_DOES_NOT_REGISTER);
        }

        String smsCode = SecurityStringUtil.getRandomSmsCode();
        String cacheKey = MemberRedisConstant.UPDATE_PWD_BY_PHONE_REDIS_KEY_PREFIX + userDO.getId();
        String smsCodeSecurityKey = SecurityStringUtil.getSmsCodeSecurityKey(cacheKey);

        // 如果验证码已经发送，则不再发送
        BusinessAssertUtil.isFalse(redisUtils.keyExists(smsCodeSecurityKey, RedisConstant.REDIS_USER_INDEX), ResponseCodeEnum.SMS_CODE_ALREADY_EXISTS);

        // 发送验证码
        smsFeignService.sendChangePasswordSms(userDO.getTelCode(), userDO.getPhone(), smsCode);
        redisUtils.stringSet(cacheKey, smsCode, smsConfig.getExpireSeconds(), RedisConstant.REDIS_USER_INDEX);
        redisUtils.stringSet(smsCodeSecurityKey, "", smsConfig.getIntervalSeconds(), RedisConstant.REDIS_USER_INDEX);
    }

    /**
     * 修改登录密码（手机校验码验证）时，检查手机短信验证码是否正确
     *
     * @param headers   Http头部信息
     * @param smsCodeReq 接口参数
     */
    @Override
    public void checkChangePasswordSmsCode(HttpHeaders headers, SmsCodeReq smsCodeReq) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        UserDO userDO = userRepository.findById(loginUser.getUserId()).orElse(null);
        if (userDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        }

        if (!StringUtils.hasLength(userDO.getPhone())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_PHONE_DOES_NOT_REGISTER);
        }

        if (memberRefreshConfig.getCheckSmsCode()){
            String cacheKey = MemberRedisConstant.UPDATE_PWD_BY_PHONE_REDIS_KEY_PREFIX + userDO.getId();
            String smsCode = memberCacheService.getString(cacheKey);
            if (!StringUtils.hasLength(smsCode)) {
                throw new BusinessException(ResponseCodeEnum.USER_SMSCODE_EXPIRED);
            }

            if (!smsCodeReq.getSmsCode().equals(smsCode)) {
                throw new BusinessException(ResponseCodeEnum.USER_SMSCODE_INCORRECT);
            }
        }

    }

    /**
     * 修改登录密码（邮箱验证）时，发送邮件验证码
     *
     * @param headers Http头部信息
     */
    @Override
    public void sendChangePasswordEmailCode(HttpHeaders headers) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        UserDO userDO = userRepository.findById(loginUser.getUserId()).orElse(null);
        if (userDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        }

        if (!StringUtils.hasLength(userDO.getEmail())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_EMAIL_DOES_NOT_REGISTER);
        }

        String smsCode = SecurityStringUtil.getRandomSmsCode();
        String cacheKey = MemberRedisConstant.UPDATE_PWD_BY_EMAIL_REDIS_KEY_PREFIX + userDO.getId();
        String smsCodeSecurityKey = SecurityStringUtil.getSmsCodeSecurityKey(cacheKey);

        // 如果验证码已经发送，则不再发送
        BusinessAssertUtil.isFalse(redisUtils.keyExists(smsCodeSecurityKey, RedisConstant.REDIS_USER_INDEX), ResponseCodeEnum.SMS_CODE_ALREADY_EXISTS);

        // 发送验证码
        memberCacheService.setString(cacheKey, smsCode, memberRefreshConfig.getMailCodeCachedTimeSeconds());
        redisUtils.stringSet(smsCodeSecurityKey, "", smsConfig.getIntervalSeconds(), RedisConstant.REDIS_USER_INDEX);

        String title = MemberStringEnum.DYNAMIC_EMAIL_CODE_TITLE.getName();
        String content = MemberStringEnum.DYNAMIC_EMAIL_CODE_TEMPLATE.getName();

        MailUtil.sendTextMail(title, String.format(content, smsCode), userDO.getEmail());

    }

    /**
     * 修改登录密码（邮箱验证）时，检查邮件验证码是否正确
     *
     * @param headers   Http头部信息
     * @param smsCodeReq 接口参数
     */
    @Override
    public void checkChangePasswordEmailCode(HttpHeaders headers, SmsCodeReq smsCodeReq) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        UserDO userDO = userRepository.findById(loginUser.getUserId()).orElse(null);
        if (userDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        }

        if (!StringUtils.hasLength(userDO.getEmail())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_EMAIL_DOES_NOT_REGISTER);
        }

        if (memberRefreshConfig.getCheckSmsCode()){
            String cacheKey = MemberRedisConstant.UPDATE_PWD_BY_EMAIL_REDIS_KEY_PREFIX + userDO.getId();
            String smsCode = memberCacheService.getString(cacheKey);
            if (!StringUtils.hasLength(smsCode)) {
                throw new BusinessException(ResponseCodeEnum.USER_SMSCODE_EXPIRED);
            }

            if (!smsCodeReq.getSmsCode().equals(smsCode)) {
                throw new BusinessException(ResponseCodeEnum.USER_SMSCODE_INCORRECT);
            }
        }

    }

    /**
     * 修改登录密码
     *
     * @param headers      Http头部信息
     * @param userManageReq 接口参数
     */
    @Override
    public void changePassword(HttpHeaders headers, UserUpdatePasswordReq userManageReq) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        UserDO userDO = userRepository.findById(loginUser.getUserId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST));

        MemberDO memberDO = userDO.getMember();
        BusinessAssertUtil.notNull(memberDO, ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);

        if (memberRefreshConfig.getCheckSmsCode()){
            // 校验验证码
            String phoneCodeCacheKey = MemberRedisConstant.UPDATE_PWD_BY_PHONE_REDIS_KEY_PREFIX + userDO.getId();
            String emailCodeCacheKey = MemberRedisConstant.UPDATE_PWD_BY_EMAIL_REDIS_KEY_PREFIX + userDO.getId();
            checkSmsCode(userManageReq.getPhoneCode(), phoneCodeCacheKey, userManageReq.getEmailCode(), emailCodeCacheKey, userManageReq.getPayPassword(), memberDO.getPayPassword());

            // 删除使用过的验证码
            eventPublisher.publishEvent(new RedisKeyRemoveEvent(this, Arrays.asList(phoneCodeCacheKey, emailCodeCacheKey)));
        }

        // 如果校验通过则更新密码
        userDO.setPassword(PasswordUtil.tryEncrypt(userManageReq.getPassword()));
        userDO.setLastModifyPwdTime(LocalDateTime.now());
        userRepository.saveAndFlush(userDO);
    }

    /**
     * 修改邮箱（手机校验码验证）时，发送手机短信验证码
     *
     * @param headers Http头部信息
     */
    @Override
    public void sendChangeEmailSmsCode(HttpHeaders headers) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        UserDO userDO = userRepository.findById(loginUser.getUserId()).orElse(null);
        if (userDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        }

        if (!StringUtils.hasLength(userDO.getPhone())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_PHONE_DOES_NOT_REGISTER);
        }

        String smsCode = SecurityStringUtil.getRandomSmsCode();
        String cacheKey = MemberRedisConstant.UPDATE_EMAIL_BY_PHONE_REDIS_KEY_PREFIX + userDO.getId();
        String smsCodeSecurityKey = SecurityStringUtil.getSmsCodeSecurityKey(cacheKey);

        // 如果验证码已经发送，则不再发送
        BusinessAssertUtil.isFalse(redisUtils.keyExists(smsCodeSecurityKey, RedisConstant.REDIS_USER_INDEX), ResponseCodeEnum.SMS_CODE_ALREADY_EXISTS);

        // 发送验证码
        smsFeignService.sendChangeEmailSms(userDO.getTelCode(), userDO.getPhone(), smsCode);
        memberCacheService.setString(cacheKey, smsCode, smsConfig.getExpireSeconds());
        redisUtils.stringSet(smsCodeSecurityKey, "", smsConfig.getIntervalSeconds(), RedisConstant.REDIS_USER_INDEX);
    }

    /**
     * 修改邮箱（手机校验码验证）时，检查手机短信验证码是否正确
     *
     * @param headers   Http头部信息
     * @param smsCodeReq 接口参数
     */
    @Override
    public void checkChangeEmailSmsCode(HttpHeaders headers, SmsCodeReq smsCodeReq) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        UserDO userDO = userRepository.findById(loginUser.getUserId()).orElse(null);
        if (userDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        }

        if (!StringUtils.hasLength(userDO.getPhone())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_PHONE_DOES_NOT_REGISTER);
        }

        if (memberRefreshConfig.getCheckSmsCode()){
            String cacheKey = MemberRedisConstant.UPDATE_EMAIL_BY_PHONE_REDIS_KEY_PREFIX + userDO.getId();
            String smsCode = memberCacheService.getString(cacheKey);
            if (!StringUtils.hasLength(smsCode)) {
                throw new BusinessException(ResponseCodeEnum.USER_SMSCODE_EXPIRED);
            }

            if (!smsCodeReq.getSmsCode().equals(smsCode)) {
                throw new BusinessException(ResponseCodeEnum.USER_SMSCODE_INCORRECT);
            }
        }
    }

    /**
     * 修改邮箱（邮箱验证）时，发送邮件验证码
     *
     * @param headers Http头部信息
     */
    @Override
    public void sendChangeEmailEmailCode(HttpHeaders headers) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        UserDO userDO = userRepository.findById(loginUser.getUserId()).orElse(null);
        if (userDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        }

        if (!StringUtils.hasLength(userDO.getEmail())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_EMAIL_DOES_NOT_REGISTER);
        }

        String smsCode = SecurityStringUtil.getRandomSmsCode();
        String cacheKey = MemberRedisConstant.UPDATE_EMAIL_BY_NEW_EMAIL_REDIS_KEY_PREFIX + userDO.getId();
        String smsCodeSecurityKey = SecurityStringUtil.getSmsCodeSecurityKey(cacheKey);

        // 如果验证码已经发送，则不再发送
        BusinessAssertUtil.isFalse(redisUtils.keyExists(smsCodeSecurityKey, RedisConstant.REDIS_USER_INDEX), ResponseCodeEnum.SMS_CODE_ALREADY_EXISTS);

        // 发送验证码
        memberCacheService.setString(cacheKey, smsCode, memberRefreshConfig.getMailCodeCachedTimeSeconds());
        redisUtils.stringSet(smsCodeSecurityKey, "", smsConfig.getIntervalSeconds(), RedisConstant.REDIS_USER_INDEX);

        String title = MemberStringEnum.DYNAMIC_EMAIL_CODE_TITLE.getName();
        String content = MemberStringEnum.DYNAMIC_EMAIL_CODE_TEMPLATE.getName();

        MailUtil.sendTextMail(title, String.format(content, smsCode), userDO.getEmail());
    }

    /**
     * 修改邮箱（邮箱验证）时，检查邮件验证码是否正确
     *
     * @param headers   Http头部信息
     * @param smsCodeReq 接口参数
     */
    @Override
    public void checkChangeEmailEmailCode(HttpHeaders headers, SmsCodeReq smsCodeReq) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        UserDO userDO = userRepository.findById(loginUser.getUserId()).orElse(null);
        if (userDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        }

        if (!StringUtils.hasLength(userDO.getEmail())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_EMAIL_DOES_NOT_REGISTER);
        }

        if (memberRefreshConfig.getCheckSmsCode()){
            String cacheKey = MemberRedisConstant.UPDATE_EMAIL_BY_NEW_EMAIL_REDIS_KEY_PREFIX + userDO.getId();
            String smsCode = memberCacheService.getString(cacheKey);
            if (!StringUtils.hasLength(smsCode)) {
                throw new BusinessException(ResponseCodeEnum.USER_SMSCODE_EXPIRED);
            }

            if (!smsCodeReq.getSmsCode().equals(smsCode)) {
                throw new BusinessException(ResponseCodeEnum.USER_SMSCODE_INCORRECT);
            }
        }

    }

    /**
     * 修改邮箱时，向新邮箱发送邮件验证码
     *
     * @param headers Http头部信息
     * @param emailReq 接口参数
     */
    @Override
    public void sendChangeEmailEmailCodeToNew(HttpHeaders headers, EmailReq emailReq) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        UserDO userDO = userRepository.findById(loginUser.getUserId()).orElse(null);
        if (userDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        }

        Specification<UserDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("email").as(String.class), emailReq.getEmail().trim()));
            list.add(criteriaBuilder.notEqual(root.get("id").as(Long.class), userDO.getId()));
            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        if (userRepository.count(specification) > 0) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_EMAIL_EXISTS);
        }

        String smsCode = SecurityStringUtil.getRandomSmsCode();
        String cacheKey = MemberRedisConstant.UPDATE_EMAIL_BY_NEW_EMAIL_REDIS_KEY_PREFIX + userDO.getId();
        String smsCodeSecurityKey = SecurityStringUtil.getSmsCodeSecurityKey(cacheKey);

        // 如果验证码已经发送，则不再发送
        BusinessAssertUtil.isFalse(redisUtils.keyExists(smsCodeSecurityKey, RedisConstant.REDIS_USER_INDEX), ResponseCodeEnum.SMS_CODE_ALREADY_EXISTS);

        // 发送验证码
        memberCacheService.setString(cacheKey, smsCode, memberRefreshConfig.getMailCodeCachedTimeSeconds());
        redisUtils.stringSet(smsCodeSecurityKey, "", smsConfig.getIntervalSeconds(), RedisConstant.REDIS_USER_INDEX);

        String title = MemberStringEnum.DYNAMIC_EMAIL_CODE_TITLE.getName();
        String content = MemberStringEnum.DYNAMIC_EMAIL_CODE_TEMPLATE.getName();

        MailUtil.sendTextMail(title, String.format(content, smsCode), emailReq.getEmail());
    }

    /**
     * 修改邮箱
     *
     * @param headers      Http头部信息
     * @param userManageReq 接口参数
     */
    @Override
    public void changeEmail(HttpHeaders headers, UserUpdateEmailReq userManageReq) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        UserDO userDO = userRepository.findById(loginUser.getUserId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST));

        MemberDO memberDO = userDO.getMember();
        BusinessAssertUtil.notNull(memberDO, ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);

        // 校验邮箱是否被其他人使用
        BusinessAssertUtil.isFalse(userRepository.existsByRelTypeAndEmailAndIdNot(MemberRelationTypeEnum.OTHER.getCode(), userManageReq.getEmail().trim(), userDO.getId()),
                ResponseCodeEnum.MC_MS_MEMBER_USER_EMAIL_EXISTS);

        if (memberRefreshConfig.getCheckSmsCode()){
            // 校验验证码
            String phoneCodeCacheKey = MemberRedisConstant.UPDATE_EMAIL_BY_PHONE_REDIS_KEY_PREFIX + userDO.getId();
            String emailCodeCacheKey = MemberRedisConstant.UPDATE_EMAIL_BY_NEW_EMAIL_REDIS_KEY_PREFIX + userDO.getId();
            checkSmsCode(userManageReq.getPhoneCode(), phoneCodeCacheKey, userManageReq.getEmailCode(), emailCodeCacheKey, userManageReq.getPayPassword(), memberDO.getPayPassword());

            // 校验新邮箱验证码
            String cacheKey = MemberRedisConstant.UPDATE_EMAIL_BY_NEW_EMAIL_REDIS_KEY_PREFIX + userDO.getId();
            String smsCode = memberCacheService.getString(cacheKey);
            BusinessAssertUtil.notBlank(smsCode, ResponseCodeEnum.USER_SMSCODE_EXPIRED);
            BusinessAssertUtil.isTrue(userManageReq.getSmsCode().equals(smsCode), ResponseCodeEnum.USER_SMSCODE_INCORRECT);

            // 删除使用过的验证码
            eventPublisher.publishEvent(new RedisKeyRemoveEvent(this, Arrays.asList(phoneCodeCacheKey, emailCodeCacheKey, cacheKey)));
        }

        // 更新用户邮箱
        userDO.setEmail(userManageReq.getEmail());
        userRepository.saveAndFlush(userDO);

        // 如果是超管，需要更改会员的邮箱
        if (UserTypeEnum.ADMIN.getCode().equals(userDO.getUserType())) {
            memberDO.setEmail(userManageReq.getEmail());
            memberRepository.saveAndFlush(memberDO);
        }
    }

    /**
     * 修改手机号码（手机校验码验证）时，发送手机短信验证码
     *
     * @param headers Http头部信息
     */
    @Override
    public void sendChangePhoneSmsCode(HttpHeaders headers) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        UserDO userDO = userRepository.findById(loginUser.getUserId()).orElse(null);
        if (userDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        }

        if (!StringUtils.hasLength(userDO.getPhone())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_PHONE_DOES_NOT_REGISTER);
        }

        String smsCode = SecurityStringUtil.getRandomSmsCode();
        String cacheKey = MemberRedisConstant.UPDATE_PHONE_BY_NEW_PHONE_REDIS_KEY_PREFIX + userDO.getId();
        String smsCodeSecurityKey = SecurityStringUtil.getSmsCodeSecurityKey(cacheKey);

        // 如果验证码已经发送，则不再发送
        BusinessAssertUtil.isFalse(redisUtils.keyExists(smsCodeSecurityKey, RedisConstant.REDIS_USER_INDEX), ResponseCodeEnum.SMS_CODE_ALREADY_EXISTS);

        // 发送验证码
        smsFeignService.sendChangePhoneSms(userDO.getTelCode(), userDO.getPhone(), smsCode);
        memberCacheService.setString(cacheKey, smsCode, smsConfig.getExpireSeconds());
        redisUtils.stringSet(smsCodeSecurityKey, "", smsConfig.getIntervalSeconds(), RedisConstant.REDIS_USER_INDEX);
    }

    /**
     * 修改手机号码（手机校验码验证）时，检查手机短信验证码是否正确
     *
     * @param headers   Http头部信息
     * @param smsCodeReq 接口参数
     */
    @Override
    public void checkPhoneSmsCode(HttpHeaders headers, SmsCodeReq smsCodeReq) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        UserDO userDO = userRepository.findById(loginUser.getUserId()).orElse(null);
        if (userDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        }

        if (!StringUtils.hasLength(userDO.getPhone())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_PHONE_DOES_NOT_REGISTER);
        }

        if (memberRefreshConfig.getCheckSmsCode()){
            String cacheKey = MemberRedisConstant.UPDATE_PHONE_BY_NEW_PHONE_REDIS_KEY_PREFIX + userDO.getId();
            String smsCode = memberCacheService.getString(cacheKey);
            if (!StringUtils.hasLength(smsCode)) {
                throw new BusinessException(ResponseCodeEnum.USER_SMSCODE_EXPIRED);
            }

            if (!smsCodeReq.getSmsCode().equals(smsCode)) {
                throw new BusinessException(ResponseCodeEnum.USER_SMSCODE_INCORRECT);
            }
        }
    }

    /**
     * 修改手机号码（邮箱验证）时，发送邮件验证码
     *
     * @param headers Http头部信息
     */
    @Override
    public void sendChangePhoneEmailCode(HttpHeaders headers) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        UserDO userDO = userRepository.findById(loginUser.getUserId()).orElse(null);
        if (userDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        }

        if (!StringUtils.hasLength(userDO.getEmail())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_EMAIL_DOES_NOT_REGISTER);
        }

        String smsCode = SecurityStringUtil.getRandomSmsCode();
        String cacheKey = MemberRedisConstant.UPDATE_PHONE_BY_EMAIL_REDIS_KEY_PREFIX + userDO.getId();
        String smsCodeSecurityKey = SecurityStringUtil.getSmsCodeSecurityKey(cacheKey);

        // 如果验证码已经发送，则不再发送
        BusinessAssertUtil.isFalse(redisUtils.keyExists(smsCodeSecurityKey, RedisConstant.REDIS_USER_INDEX), ResponseCodeEnum.SMS_CODE_ALREADY_EXISTS);

        // 发送验证码
        memberCacheService.setString(cacheKey, smsCode, memberRefreshConfig.getMailCodeCachedTimeSeconds());
        redisUtils.stringSet(smsCodeSecurityKey, "", smsConfig.getIntervalSeconds(), RedisConstant.REDIS_USER_INDEX);

        String title = MemberStringEnum.DYNAMIC_EMAIL_CODE_TITLE.getName();
        String content = MemberStringEnum.DYNAMIC_EMAIL_CODE_TEMPLATE.getName();

        MailUtil.sendTextMail(title, String.format(content, smsCode), userDO.getEmail());
    }

    /**
     * 修改手机号码（邮箱验证）时，检查邮件验证码是否正确
     *
     * @param headers   Http头部信息
     * @param smsCodeReq 接口参数
     */
    @Override
    public void checkChangePhoneEmailCode(HttpHeaders headers, SmsCodeReq smsCodeReq) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        UserDO userDO = userRepository.findById(loginUser.getUserId()).orElse(null);
        if (userDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        }

        if (!StringUtils.hasLength(userDO.getEmail())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_EMAIL_DOES_NOT_REGISTER);
        }

        if (memberRefreshConfig.getCheckSmsCode()){
            String cacheKey = MemberRedisConstant.UPDATE_PHONE_BY_EMAIL_REDIS_KEY_PREFIX + userDO.getId();
            String smsCode = memberCacheService.getString(cacheKey);
            if (!StringUtils.hasLength(smsCode)) {
                throw new BusinessException(ResponseCodeEnum.USER_SMSCODE_EXPIRED);
            }

            if (!smsCodeReq.getSmsCode().equals(smsCode)) {
                throw new BusinessException(ResponseCodeEnum.USER_SMSCODE_INCORRECT);
            }
        }
    }

    /**
     * 修改手机号码时，向新手机号码发送短信验证码
     *
     * @param headers Http头部信息
     * @param phoneReq 接口参数
     */
    @Override
    public void sendChangePhoneSmsCodeToNew(HttpHeaders headers, PhoneReq phoneReq) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        UserDO userDO = userRepository.findById(loginUser.getUserId()).orElse(null);
        if (userDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        }

        if (!StringUtils.hasLength(userDO.getPhone())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_PHONE_DOES_NOT_REGISTER);
        }

        Specification<UserDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("phone").as(String.class), phoneReq.getPhone().trim()));
            list.add(criteriaBuilder.notEqual(root.get("id").as(Long.class), userDO.getId()));
            list.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), userDO.getRelType()));
            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        if (userRepository.count(specification) > 0) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_PHONE_EXISTS);
        }

        String smsCode = SecurityStringUtil.getRandomSmsCode();
        String cacheKey = MemberRedisConstant.UPDATE_PHONE_BY_NEW_PHONE_REDIS_KEY_PREFIX + userDO.getId() + ":" + phoneReq.getPhone();
        String smsCodeSecurityKey = SecurityStringUtil.getSmsCodeSecurityKey(cacheKey);

        // 如果验证码已经发送，则不再发送
        BusinessAssertUtil.isFalse(redisUtils.keyExists(smsCodeSecurityKey, RedisConstant.REDIS_USER_INDEX), ResponseCodeEnum.SMS_CODE_ALREADY_EXISTS);

        // 发送验证码
        smsFeignService.sendChangePhoneSms(phoneReq.getTelCode(), phoneReq.getPhone(), smsCode);
        memberCacheService.setString(cacheKey, smsCode, smsConfig.getExpireSeconds());
        redisUtils.stringSet(smsCodeSecurityKey, "", smsConfig.getIntervalSeconds(), RedisConstant.REDIS_USER_INDEX);
    }

    /**
     * 修改手机号码时，检查向新手机号码发送的短信验证码是否正确
     *
     * @param headers   Http头部信息
     * @param smsCodeVO 接口参数
     */
    @Override
    public void checkNewPhoneSmsCode(HttpHeaders headers, PhoneSmsReq smsCodeVO) {
        if (!memberRefreshConfig.getCheckSmsCode()) {
            return ;
        }

        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        String cacheKey = MemberRedisConstant.UPDATE_PHONE_BY_NEW_PHONE_REDIS_KEY_PREFIX + loginUser.getUserId() + ":" + smsCodeVO.getPhone();
        String smsCode = memberCacheService.getString(cacheKey);
        if (!StringUtils.hasLength(smsCode)) {
            throw new BusinessException(ResponseCodeEnum.USER_SMSCODE_EXPIRED);
        }

        if (!smsCodeVO.getSmsCode().equals(smsCode)) {
            throw new BusinessException(ResponseCodeEnum.USER_SMSCODE_INCORRECT);
        }
    }

    /**
     * 修改手机号
     *
     * @param headers      Http头部信息
     * @param userManageReq 接口参数
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void changePhone(HttpHeaders headers, UserUpdatePhoneReq userManageReq) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        UserDO userDO = userRepository.findById(loginUser.getUserId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST));

        MemberDO memberDO = userDO.getMember();
        BusinessAssertUtil.notNull(memberDO, ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);

        // 查询手机号码是否已经存在(同一家公司中不能重复)
        BusinessAssertUtil.isFalse(userRepository.existsByRelTypeAndPhoneAndIdNot(MemberRelationTypeEnum.OTHER.getCode(), userManageReq.getPhone().trim(), userDO.getId()),
                ResponseCodeEnum.MC_MS_MEMBER_USER_PHONE_EXISTS);

        if (memberRefreshConfig.getCheckSmsCode()){
            // 校验验证码
            String phoneCodeCacheKey = MemberRedisConstant.UPDATE_PHONE_BY_NEW_PHONE_REDIS_KEY_PREFIX + userDO.getId();
            String emailCodeCacheKey = MemberRedisConstant.UPDATE_PHONE_BY_EMAIL_REDIS_KEY_PREFIX + userDO.getId();
            checkSmsCode(userManageReq.getPhoneCode(), phoneCodeCacheKey, userManageReq.getEmailCode(), emailCodeCacheKey, userManageReq.getPayPassword(), memberDO.getPayPassword());

            // 校验新手机验证码
            String cacheKey = MemberRedisConstant.UPDATE_PHONE_BY_NEW_PHONE_REDIS_KEY_PREFIX + loginUser.getUserId() + ":" + userManageReq.getPhone();
            String smsCode = memberCacheService.getString(cacheKey);
            BusinessAssertUtil.notBlank(smsCode, ResponseCodeEnum.USER_SMSCODE_EXPIRED);
            BusinessAssertUtil.isTrue(userManageReq.getSmsCode().equals(smsCode), ResponseCodeEnum.USER_SMSCODE_INCORRECT);

            // 删除使用过的验证码
            eventPublisher.publishEvent(new RedisKeyRemoveEvent(this, Arrays.asList(phoneCodeCacheKey, emailCodeCacheKey, cacheKey)));
        }

        // TODO: 2023/9/21 下个版本开发会员多主体后，需要兼容手机号和邮箱的相关逻辑，此处先按照原先的
//        BusinessAssert.isFalse(userRepository.existsByRelTypeAndPhoneAndMemberIdAndIdNot(MemberRelationTypeEnum.OTHER.getCode(), userManageReq.getPhone().trim(), memberDO.getId(), userDO.getId()),
//                ResponseCode.MC_MS_MEMBER_USER_PHONE_EXISTS);

        // 更新用户手机号
        userDO.setPhone(userManageReq.getPhone());
        //如果是会员的管理员用户：
        //账号就是手机号，所以要更新两个字段
        //会员的账号也是手机号
        if (UserTypeEnum.ADMIN.getCode().equals(userDO.getUserType())) {
            userDO.setAccount(userManageReq.getPhone());

            memberDO.setAccount(userManageReq.getPhone());
            memberDO.setPhone(userManageReq.getPhone());
            memberRepository.saveAndFlush(memberDO);
        }

        userRepository.saveAndFlush(userDO);
    }

    /**
     * 重置支付密码时，发送手机短信验证码
     *
     * @param headers Http头部信息
     */
    @Override
    public void sendChangePayPswSmsCode(HttpHeaders headers) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        UserDO userDO = userRepository.findById(loginUser.getUserId()).orElse(null);
        if (userDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        }

        if (!StringUtils.hasLength(userDO.getPhone())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_PHONE_DOES_NOT_REGISTER);
        }

        String smsCode = SecurityStringUtil.getRandomSmsCode();
        String cacheKey = MemberRedisConstant.UPDATE_PAY_PWD_BY_PHONE_REDIS_KEY_PREFIX + userDO.getId();
        String smsCodeSecurityKey = SecurityStringUtil.getSmsCodeSecurityKey(cacheKey);

        // 如果验证码已经发送，则不再发送
        BusinessAssertUtil.isFalse(redisUtils.keyExists(smsCodeSecurityKey, RedisConstant.REDIS_USER_INDEX), ResponseCodeEnum.SMS_CODE_ALREADY_EXISTS);

        // 发送验证码
        smsFeignService.sendChangePayPasswordSms(userDO.getTelCode(), userDO.getPhone(), smsCode);
        memberCacheService.setString(cacheKey, smsCode, smsConfig.getExpireSeconds());
        redisUtils.stringSet(smsCodeSecurityKey, "", smsConfig.getIntervalSeconds(), RedisConstant.REDIS_USER_INDEX);
    }

    /**
     * 重置支付密码时，检查手机短信验证码是否正确
     *
     * @param headers   Http头部信息
     * @param smsCodeReq 接口参数
     */
    @Override
    public void checkChangePayPswSmsCode(HttpHeaders headers, SmsCodeReq smsCodeReq) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        UserDO userDO = userRepository.findById(loginUser.getUserId()).orElse(null);
        if (userDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        }

        if (!StringUtils.hasLength(userDO.getPhone())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_PHONE_DOES_NOT_REGISTER);
        }

        if (memberRefreshConfig.getCheckSmsCode()){
            String cacheKey = MemberRedisConstant.UPDATE_PAY_PWD_BY_PHONE_REDIS_KEY_PREFIX + userDO.getId();
            String smsCode = memberCacheService.getString(cacheKey);
            if (!StringUtils.hasLength(smsCode)) {
                throw new BusinessException(ResponseCodeEnum.USER_SMSCODE_EXPIRED);
            }

            if (!smsCodeReq.getSmsCode().equals(smsCode)) {
                throw new BusinessException(ResponseCodeEnum.USER_SMSCODE_INCORRECT);
            }
        }
    }

    /**
     * 旧密码验证后，修改支付密码
     *
     * @param headers      Http头部信息
     * @param userManageReq 接口参数
     */
    @Override
    public void changePayPassword(HttpHeaders headers, UserUpdatePayPasswordReq userManageReq) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        String branchId = headers.getFirst(Constant.BRANCH_ID);
        if(StringUtils.isEmpty(branchId)){
           throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RUN_BRAND_CODE_NOT_EXISTS);
        }
        UserDO userDO = userRepository.findById(loginUser.getUserId()).orElseThrow(()-> new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST));
        MemberBranchDO memberBranchDO = memberBranchRepository.findById(Long.valueOf(branchId)).orElse(null);
        if(memberBranchDO == null){
            throw new BusinessException(ResponseCodeEnum.ORDER_RELATION_BRANCH_NOT_EXIST);
        }
        MemberDO memberDO = memberRepository.findById(memberBranchDO.getMemberId()).orElse(null);
        BusinessAssertUtil.notNull(memberDO, ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        if (memberRefreshConfig.getCheckSmsCode()){
            // 校验验证码
            String cacheKey = MemberRedisConstant.UPDATE_PAY_PWD_BY_PHONE_REDIS_KEY_PREFIX + userDO.getId();
            String smsCode = memberCacheService.getString(cacheKey);
            BusinessAssertUtil.notBlank(smsCode, ResponseCodeEnum.USER_SMSCODE_EXPIRED);
            BusinessAssertUtil.isTrue(smsCode.equals(userManageReq.getPhoneCode()), ResponseCodeEnum.USER_SMSCODE_INCORRECT);

            // 删除使用过的验证码
            eventPublisher.publishEvent(new RedisKeyRemoveEvent(this, Collections.singletonList(cacheKey)));
        }else{
            if(StringUtils.hasText(memberDO.getPayPassword()) && !PasswordUtil.tryEncrypt(userManageReq.getOldPayPassword()).equals(memberDO.getPayPassword())){
                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_OLD_PASSWORD_ERROR);
            }
        }
        // 设置新密码
        memberDO.setPayPassword(PasswordUtil.tryEncrypt(userManageReq.getPayPassword()));
        memberRepository.saveAndFlush(memberDO);
    }

    /**
     * 平台后台 - 更改用户密码
     *
     * @param headers    Http头部信息
     * @param passwordVO 接口参数
     */
    @Override
    public void updatePlatformUserPassword(HttpHeaders headers, PlatformUpdatePasswordReq passwordVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromManagePlatform(headers);

        // 手机号和邮箱不能同时为空
        BusinessAssertUtil.notAllBlank(Arrays.asList(passwordVO.getPhone(), passwordVO.getEmail()).toArray(new CharSequence[0]), ResponseCodeEnum.MC_MS_PHONE_OR_EMAIL_ALL_NULL);

        // 手机验证码和邮箱验证码不能同时为空
        BusinessAssertUtil.notAllBlank(Arrays.asList(passwordVO.getPhoneSmsCode(), passwordVO.getEmailSmsCode()).toArray(new CharSequence[0]), ResponseCodeEnum.MC_MS_PHONE_OR_EMAIL_SMS_CODE_ALL_NULL);

        // 查找用户
        UserDO userDO = userRepository.findById(loginUser.getUserId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST));

        // 校验旧密码
        BusinessAssertUtil.isTrue(PasswordUtil.checkPassword(userDO.getPassword(), passwordVO.getOldPassword()), ResponseCodeEnum.MC_MS_OLD_PASSWORD_INCORRECT);

        // 校验验证码
        if (memberRefreshConfig.getCheckSmsCode()) {
            String cacheKey;
            if (StringUtils.hasLength(passwordVO.getPhone())) {
                cacheKey = MemberRedisConstant.UPDATE_PWD_BY_PHONE_REDIS_KEY_PREFIX + userDO.getId();
                BusinessAssertUtil.isTrue(Objects.equals(memberCacheService.getString(cacheKey), passwordVO.getPhoneSmsCode()), ResponseCodeEnum.USER_SMSCODE_EXPIRED);
            } else {
                cacheKey = MemberRedisConstant.UPDATE_PWD_BY_EMAIL_REDIS_KEY_PREFIX + userDO.getId();
                BusinessAssertUtil.isTrue(Objects.equals(memberCacheService.getString(cacheKey), passwordVO.getEmailSmsCode()), ResponseCodeEnum.USER_SMSCODE_EXPIRED);
            }

            // 删除使用过的验证码
            eventPublisher.publishEvent(new RedisKeyRemoveEvent(this, Collections.singletonList(cacheKey)));
        }

        // 设置新密码
        userDO.setPassword(PasswordUtil.tryEncrypt(passwordVO.getNewPassword()));
        userRepository.save(userDO);
    }

    /**
     * 修改绑定信息时校验修改方式是否合法
     *
     * @param phoneCode         手机验证码
     * @param phoneCodeCacheKey 手机验证码缓存key
     * @param emailCode         邮箱验证码
     * @param emailCodeCacheKey 邮箱验证码缓存key
     * @param payPasswordReq    支付密码Req
     * @param payPassword       支付密码
     */
    public void checkSmsCode(String phoneCode, String phoneCodeCacheKey, String emailCode, String emailCodeCacheKey, String payPasswordReq, String payPassword) {
        // 三种校验方式至少要有一种
        BusinessAssertUtil.notAllBlank(Arrays.asList(phoneCode, emailCode, payPasswordReq).toArray(new CharSequence[0]), ResponseCodeEnum.USER_SMSCODE_INCORRECT);

        // 校验支付密码
        if (StringUtils.hasText(payPasswordReq)) {
            BusinessAssertUtil.isTrue(PasswordUtil.checkPassword(payPassword, payPasswordReq), ResponseCodeEnum.MC_MS_PAY_PASSWORD_INCORRECT);
        }

        // 校验手机验证码
        if (StringUtils.hasText(phoneCode)) {
            String smsCode = memberCacheService.getString(phoneCodeCacheKey);
            BusinessAssertUtil.notBlank(smsCode, ResponseCodeEnum.USER_SMSCODE_EXPIRED);
            BusinessAssertUtil.isTrue(phoneCode.equals(smsCode), ResponseCodeEnum.USER_SMSCODE_INCORRECT);
        }

        // 校验邮箱验证码
        if (StringUtils.hasText(emailCode)) {
            String smsCode = memberCacheService.getString(emailCodeCacheKey);
            BusinessAssertUtil.notBlank(smsCode, ResponseCodeEnum.USER_SMSCODE_EXPIRED);
            BusinessAssertUtil.isTrue(emailCode.equals(smsCode), ResponseCodeEnum.USER_SMSCODE_INCORRECT);
        }
    }

    /**
     * 账户安全-实名验证-显示已实名认证的信息列表
     *
     * @param headers Http头部信息
     * @return 操作结果
     */
    @Override
    public UserAccountAuthQueryResp getUserInfo(HttpHeaders headers) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        UserDO userDO = userRepository.findById(loginUser.getUserId()).orElse(null);
        if (userDO != null) {
            UserAccountAuthQueryResp vo = new UserAccountAuthQueryResp();
            vo.setUserId(userDO.getId());
            //名称脱敏
            vo.setName(NameUtil.process(userDO.getRealName()));
            //身份证脱敏
            if (!StringUtils.hasLength(userDO.getIdCardNo())) {
                vo.setCardNo(userDO.getIdCardNo().replaceAll("(\\d)\\d{16}(\\w)", "$1****************$2"));
            }
            Map<String, Object> idCardImg = userDO.getIdCardImg();
            if (!CollectionUtils.isEmpty(idCardImg)) {
                vo.setFrontUrl((String) idCardImg.get("front"));
                vo.setBackUrl((String) idCardImg.get("back"));
            }
            return vo;
        }
        return null;
    }

    /**
     * 账户安全-实名验证-上传身份证信息
     *
     * @return 操作结果
     */
    @Override
    public UserAccountAuthQueryResp uploadIdCard(UserUploadIdCardReq userUploadIdCardReq) {
        //1.先从redis中获取assess_token
        RealNameConfigDO realNameConfigDO = memberAuthTypeConfigFeignService.findEnableAuthConfig();
        HashMap<String, Object> config = realNameConfigDO.getConfig();
        String apiKey = (String) config.get(BaiDuConfigEnum.API_KEY.getCode());
        String secretKey = (String) config.get(BaiDuConfigEnum.SECRET_KEY.getCode());
        String aesKey = (String) config.get(BaiDuConfigEnum.AES_KEY.getCode());
        if (!StringUtils.hasLength(apiKey) || !StringUtils.hasLength(secretKey) || !StringUtils.hasLength(aesKey)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_CONFIGURE_AUTH_INFO_FIRST);
        }
        //切割数据
        apiKey = apiKey.substring(0, apiKey.indexOf("|"));
        secretKey = secretKey.substring(0, secretKey.indexOf("|"));
        aesKey = aesKey.substring(0, aesKey.indexOf("|"));
        String assessToken = AuthUtil.getAuth(apiKey, secretKey);
        String frontResult;
        //2.上传正面图片信息
        try {
            frontResult = IdCardUtil.idCard(userUploadIdCardReq.getFrontUrl(), aesKey, assessToken);
        } catch (Exception e) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_INCORRECT_CONFIGURATION);
        }
        //3.解析返回结果
        JSONObject frontResp = JSONObject.parseObject(frontResult);
        String frontWordsResult = frontResp.getString("words_result");
        JSONObject attributes = JSONObject.parseObject(frontWordsResult);
        String nameKey = attributes.getString("姓名");
        String cardNoKey = attributes.getString("公民身份号码");
        String addressKey = attributes.getString("住址");
        if (!StringUtils.hasLength(nameKey) || !StringUtils.hasLength(cardNoKey) || !StringUtils.hasLength(addressKey)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_UPLOAD_REALLY_FRONT_URL);
        }
        JSONObject nameResp = JSONObject.parseObject(nameKey);
        JSONObject cardNoResp = JSONObject.parseObject(cardNoKey);
        JSONObject addressResp = JSONObject.parseObject(addressKey);
        String name = nameResp.getString("words");
        String cardNo = cardNoResp.getString("words");
        String address = addressResp.getString("words");
        if (!StringUtils.hasLength(name) || !StringUtils.hasLength(cardNo) || !StringUtils.hasLength(address)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_REAL_NAME_VERIFICATION_FAILED);
        }
        //4.上传背面图片信息
        String backResult;
        try {
            backResult = IdCardUtil.idCard(userUploadIdCardReq.getBackUrl(), aesKey, assessToken);
        } catch (Exception e) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_INCORRECT_CONFIGURATION);
        }
        JSONObject backResp = JSONObject.parseObject(backResult);
        String backWordsResult = backResp.getString("words_result");
        JSONObject backAttributes = JSONObject.parseObject(backWordsResult);
        String expirationDate = backAttributes.getString("失效日期");
        String dateOfIssue = backAttributes.getString("签发日期");
        String issuingAuthority = backAttributes.getString("签发机关");
        if (!StringUtils.hasLength(expirationDate) || !StringUtils.hasLength(dateOfIssue) || !StringUtils.hasLength(issuingAuthority)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_UPLOAD_REALLY_BACK_URL);
        }

        JSONObject expirationResp = JSONObject.parseObject(expirationDate);
        JSONObject dateOfIssueResp = JSONObject.parseObject(dateOfIssue);
        JSONObject issuingAuthorityResp = JSONObject.parseObject(issuingAuthority);
        Long expirationR = expirationResp.getLong("words");
        Long dateOfIssueR = dateOfIssueResp.getLong("words");
        String issuingAuthorityR = issuingAuthorityResp.getString("words");
        long localDateTime = Long.parseLong(DateTimeUtil.formatLocalDateTime());//当前日期
        if (localDateTime <= dateOfIssueR || localDateTime >= expirationR) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_ID_CARD_DATA_INVALID);
        }
        String substring = issuingAuthorityR.substring(0, issuingAuthorityR.length() - 3);//XXX公安局
        if (!address.contains(substring)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_UPLOAD_REALLY_BACK_URL);
        }
        UserAccountAuthQueryResp vo = new UserAccountAuthQueryResp();
        vo.setName(name);
        vo.setCardNo(cardNo);
        return vo;
    }

    /**
     * 账户安全-实名验证-保存身份证信息
     *
     * @param headers        Http头部信息
     * @param userAuthInfoReq 实名信息请求体
     */
    @Override
    public void saveAuthInfo(HttpHeaders headers, UserAuthInfoReq userAuthInfoReq) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        UserDO userDO = userRepository.findById(loginUser.getUserId()).orElse(null);
        if (userDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        }
        userDO.setRealName(userAuthInfoReq.getName());
        userDO.setIdCardNo(userAuthInfoReq.getCardNo());
        Map<String, Object> map = new HashMap<>();
        map.put("front", userAuthInfoReq.getFrontUrl());
        map.put("back", userAuthInfoReq.getBackUrl());
        userDO.setIdCardImg(map);
        userDO.setIsAuth(Boolean.TRUE);//已实名验证
        userRepository.saveAndFlush(userDO);
    }

    @Override
    public Integer memberCancellationEnumType(HttpHeaders headers) {
        return ContentNoticeTypeEnum.MALL_ACCOUNT_CANCELLATION_AGREEMENT.getCode();
    }

    /**
     * 会员注销 - 校验
     *
     * @param headers Http头部信息
     * @return 操作结果
     */
    @Override
    public MemberCancellationFailResp memberCancellationCheck(HttpHeaders headers) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        Long userId = loginUser.getUserId();
        Long memberId = loginUser.getMemberId();
        MemberCancellationFailResp result = new MemberCancellationFailResp();
        UserDO userDO = userRepository.findById(userId).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST));

        //	*会员状态为正常
        if (Objects.isNull(userDO)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }
        if (!userDO.getUserType().equals(UserTypeEnum.ADMIN.getCode())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_ADMIN_ROLE_NOT_EXIST);
        }

        // 1、如果账号不是有效状态，则提示"您的当前账号处于冻结状态，请联系客服处理"
        final Integer relType = MemberRelationTypeEnum.PLATFORM.getCode();
        List<MemberRelationDO> memberRelationList = relationRepository.findBySubMemberIdAndRelType(memberId, relType);
        if (CollectionUtils.isEmpty(memberRelationList)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }
        MemberRelationDO memberRelationDO = memberRelationList.get(0);
        List<Integer> statusList = new ArrayList<>();
        statusList.add(MemberInnerStatusEnum.TO_MODIFY_GRADE_ONE.getCode());
        statusList.add(MemberInnerStatusEnum.DEPOSITORY_DETAIL_NOT_PASSED.getCode());
        statusList.add(MemberInnerStatusEnum.DEPOSITORY_QUALIFICATION_NOT_PASSED.getCode());
        statusList.add(MemberInnerStatusEnum.DEPOSITORY_INSPECTION_NOT_PASSED.getCode());
        statusList.add(MemberInnerStatusEnum.DEPOSITORY_CLASSIFICATION_NOT_PASSED.getCode());
        statusList.add(MemberInnerStatusEnum.DEPOSITORY_GRADE_ONE_NOT_PASSED.getCode());
        statusList.add(MemberInnerStatusEnum.DEPOSITORY_GRADE_TWO_NOT_PASSED.getCode());
        statusList.add(MemberInnerStatusEnum.MODIFY_GRADE_ONE_NOT_PASSED.getCode());
        statusList.add(MemberInnerStatusEnum.MODIFY_GRADE_TWO_NOT_PASSED.getCode());
        statusList.add(MemberInnerStatusEnum.MODIFY_NOT_PASSED.getCode());
        if (statusList.contains(memberRelationDO.getInnerStatus())) {
            result.setMemberStatus(false);
        }

        // 2、如果账号内有未完状态订单，则提示“您的账号内有未完成的订单，请先完成订单交易后再操作。”
        OrderFeignMemberIdReq feignVO = new OrderFeignMemberIdReq();
        feignVO.setMemberId(memberId);
        WrapperResp<Boolean> orederCheckBoolean = orderFeignService.checkUnclosedOrder(feignVO);
        log.info("订单服务会员id:{},返回信息:{}", memberId, JSON.toJSONString(orederCheckBoolean));
        if (orederCheckBoolean.getCode() != 1000) {
            throw new BusinessException(ResponseCodeEnum.SERVICE_ORDER_ERROR);
        }
        if (!orederCheckBoolean.getData()) {
            result.setNotCompleteOrder(false);
        }

        // 3、如果账号内有未完成的售后记录，则提示“您的账号内有未完成的售后，请先完成售后处理后再操作。”
        MemberIdReq memberRequest = new MemberIdReq();
        memberRequest.setMemberId(memberId);
        WrapperResp<Boolean> afterOrderBoolean = afterCommonFeignService.isUnfinishedAfter(memberRequest);
        log.info("售后服务会员id:{},返回信息:{}", memberId, JSON.toJSONString(afterOrderBoolean));
        if (afterOrderBoolean.getCode() != 1000) {
            throw new BusinessException(ResponseCodeEnum.SERVICE_AFTERSALES_ERROR);
        }
        if (afterOrderBoolean.getData()) {
            result.setNotCompleteAfterSale(false);
        }

        // 4、如果账号内有供应商角色标签，则提示“您的账号内有供应商/商家角色，暂不支持账号注销。”
        List<Long> roleIdList = memberRepository.findByRoleIdByMemberId(memberId);
        // 根据角色id查询供应商
        final Integer supplierRoleTag = RoleTagEnum.SUPPLIER.getCode();
        if (!CollectionUtils.isEmpty(roleIdList)) {
            List<MemberRelationDO> supplierList = relationRepository.findByMemberIdAndSubRoleTag(memberId, supplierRoleTag, roleIdList);
            if (!CollectionUtils.isEmpty(supplierList) && supplierList.size() != 1) {
                for (MemberRelationDO supplierVo : supplierList) {
                    if (supplierVo.getStatus().equals(MemberStatusEnum.NORMAL.getCode()) ||
                            supplierVo.getStatus().equals(MemberStatusEnum.CORRECTING.getCode())) {
                        result.setNotCompleteStore(false);
                        break;
                    }
                }
            }
        }

        // 5、如果账号内的平台账号余额或店铺账号余额不为0，则提示"您的平台账号余额不为0（或店铺账号余额不为0），请先提现余额再操作。"
        CheckAssetAccountReq request = new CheckAssetAccountReq();
        request.setMemberId(memberId);
        WrapperResp<Boolean> checkAssetAccount = assetAccountFeignService.checkAssetAccount(request);
        log.info("支付服务会员id:{},返回信息:{}", memberId, JSON.toJSONString(checkAssetAccount));
        if (checkAssetAccount.getCode() != 1000) {
            throw new BusinessException(ResponseCodeEnum.SERVICE_PAY_ERROR);
        }
        if (checkAssetAccount.getData()) {
            result.setAccountBalance(false);
        }

        // 6、如果账号内有未完成的应收应付的结算记录和有未还款的授信记录，则提示"您的账号内有未完成的应收应付结算数据/授信欠款，请先完成结算/还款再操作。"
        SettlementMemberIdReq settlementMemberRequest = new SettlementMemberIdReq();
        settlementMemberRequest.setMemberId(memberId);
        WrapperResp<Boolean> memberSettlementBoolean = memberSettlementControllerFeignService.isUnfinishedMemberSettlement(settlementMemberRequest);
        log.info("应收服务会员id:{},返回信息:{}", memberId, JSON.toJSONString(memberSettlementBoolean));
        if (memberSettlementBoolean.getCode() != 1000) {
            throw new BusinessException(ResponseCodeEnum.SERVICE_SETTLEMENT_ERROR);
        }
        if (memberSettlementBoolean.getData()) {
            result.setNotCompleteSettlement(false);
        }

        MemberDO memberDO = new MemberDO();
        memberDO.setId(memberId);
        // 7、如果账号内有待处理的投诉，则提示“您的账号内有正在处理的投诉事件，请先待投诉事件完成后再操作。”
        Integer type = 1;
        List<MemberComplaintDO> memberComplaintList = memberComplaintRepository.findByMemberAndType(memberDO, type);
        if (!CollectionUtils.isEmpty(memberComplaintList)) {
            for (MemberComplaintDO memberComplaintDO : memberComplaintList) {
                // 状态 1-待提交投诉建议 2-待处理投诉建议 3-已处理投诉建议
                if (memberComplaintDO.getStatus().equals(2)) {
                    result.setNotCompleteComplaint(false);
                    break;
                }
            }
        }

        // 8、如果账号内有启用中的用户子账号(超级管理员账户除外），则提示"您的账号内有可用的用户子账号，请先停用用户子账号后台再操作。
        List<UserDO> memberUserDOList = userRepository.findByMember(memberDO);
        if (!CollectionUtils.isEmpty(memberUserDOList)) {
            for (UserDO user : memberUserDOList) {
                if (user.getUserType().equals(UserTypeEnum.ADMIN.getCode())) {
                    continue;
                }
                if (user.getStatus().equals(EnableDisableStatusEnum.ENABLE.getCode())) {
                    result.setSubUserStatus(false);
                    break;
                }
            }
        }
        result.setCountryCode(userDO.getTelCode());
        result.setPhone(userDO.getPhone());
        return result;
    }

    /**
     * 会员注销 - 获取验证码
     *
     * @param headers Http头部信息
     */
    @Override
    public void sendCancellationMemberSmsCode(HttpHeaders headers) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        UserDO userDO = userRepository.findById(loginUser.getUserId()).orElse(null);

        BusinessAssertUtil.notNull(userDO, ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        BusinessAssertUtil.isTrue(StringUtils.hasLength(userDO.getPhone()), ResponseCodeEnum.MC_MS_MEMBER_USER_PHONE_DOES_NOT_REGISTER);

        String smsCode = SecurityStringUtil.getRandomSmsCode();
        String cacheKey = MemberRedisConstant.MEMBER_CANCELLATION_BY_PHONE_REDIS_KEY_PREFIX + userDO.getId();
        String smsCodeSecurityKey = SecurityStringUtil.getSmsCodeSecurityKey(cacheKey);

        // 如果验证码已经发送，则不再发送
        BusinessAssertUtil.isFalse(redisUtils.keyExists(smsCodeSecurityKey, RedisConstant.REDIS_USER_INDEX), ResponseCodeEnum.SMS_CODE_ALREADY_EXISTS);

        // 发送验证码
        smsFeignService.sendMemberCancellationSms(userDO.getTelCode(), userDO.getPhone(), smsCode);
        memberCacheService.setString(cacheKey, smsCode, smsConfig.getExpireSeconds());
        redisUtils.stringSet(smsCodeSecurityKey, "", smsConfig.getIntervalSeconds(), RedisConstant.REDIS_USER_INDEX);
    }

    /**
     * 会员注销 - 确认注销接口
     * 重新做一次注销校验
     * 修改关系表
     *
     *
     * @param headers   Http头部信息
     * @param cancellationReq 接口参数
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public MemberCancellationFailResp cancellationMember(HttpHeaders headers, CancellationMemberReq cancellationReq) {
        // 校验当前账号是否是超级管理员
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        BusinessAssertUtil.isTrue(UserTypeEnum.ADMIN.getCode().equals(loginUser.getUserType()), ResponseCodeEnum.NO_PERMISSION);

        // 校验短信验证码
        if (memberRefreshConfig.getCheckSmsCode()) {
            String cacheKey = MemberRedisConstant.MEMBER_CANCELLATION_BY_PHONE_REDIS_KEY_PREFIX + loginUser.getUserId();
            String smsCode = memberCacheService.getString(cacheKey);
            BusinessAssertUtil.isTrue(StringUtils.hasLength(smsCode), ResponseCodeEnum.USER_SMSCODE_EXPIRED);
            BusinessAssertUtil.isTrue(Objects.equals(cancellationReq.getSmsCode(), smsCode), ResponseCodeEnum.USER_SMSCODE_INCORRECT);

            // 删除使用过的验证码
            eventPublisher.publishEvent(new RedisKeyRemoveEvent(this, Collections.singletonList(cacheKey)));
        }

        // 再重新做一次注销校验（状态、订单、售后...等）
        MemberCancellationFailResp resultVo = this.memberCancellationCheck(headers);
        // 查询会员所有的上级会员
        List<MemberRelationDO> relDOList = relationRepository.findAllBySubMemberId(loginUser.getMemberId());
        BusinessAssertUtil.notEmpty(relDOList, ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);

        // 设置相关关系表状态
        for (MemberRelationDO relDO : relDOList) {
            if(MemberStatusEnum.WAIT_CANCELLATION.getCode().equals(relDO.getStatus())){
                throw new BusinessException(ResponseCodeEnum.MC_MS_PLEASE_DO_NOT_RESUBMIT_CANCELLATION);
            }

            // 记录注销表
            MemberCancellationDO cancellationDO = relDO.getMemberCancellation();
            if (Objects.isNull(cancellationDO)) {
                cancellationDO = new MemberCancellationDO();
            }
            cancellationDO.setApplyCancellationTime(LocalDateTime.now());
            cancellationDO.setCancellationReason(cancellationReq.getCancellationReason());
            cancellationDO.setCancellationBeforeStatus(relDO.getStatus());
            cancellationDO.setCancellationStatus(MemberCancellationEnum.WAIT_CANCELLATION.getCode());
            cancellationDO.setRelation(relDO);
            cancellationRepository.save(cancellationDO);

            // 记录关系表
            relDO.setStatus(MemberStatusEnum.WAIT_CANCELLATION.getCode());
            relDO.setMemberCancellation(cancellationDO);
            relationRepository.save(relDO);

            //内部记录
            baseMemberHistoryService.saveMemberInnerHistory(relDO, loginUser, MemberValidateHistoryOperationEnum.APPLY_FOR_CANCELLATION, cancellationReq.getCancellationReason());

            //外部记录
            baseMemberHistoryService.saveMemberOuterHistory(relDO, relDO.getRole().getRoleName(), MemberValidateHistoryOperationEnum.APPLY_FOR_CANCELLATION, MemberStatusEnum.getCodeMessage(relDO.getStatus()), cancellationReq.getCancellationReason());
        }

        return resultVo;
    }

    @Override
    public void sendManageLoginPhoneSmsCode(HttpHeaders headers, PhoneLoginSmsCode phoneReq) {
        memberCacheService.checkPlatformRequestHeader(headers);

        // 查找用户
        UserDO userDO = userRepository.findFirstByPhoneAndRelType(phoneReq.getPhone(), MemberRelationTypeEnum.PLATFORM.getCode());
        BusinessAssertUtil.notNull(userDO, ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);

        // 校验国家手机号代码
        CountryAreaResp countryAreaResp = WrapperUtil.getDataOrThrow(countryAreaFeign.getCountryAreaByTelCode(phoneReq.getTelCode()), ResponseCodeEnum.MC_MS_COUNTRY_CODE_DOES_NOT_EXIST);

        // 校验手机号格式
        if (!SecurityStringUtil.checkPhone(countryAreaResp.getTelCode(), phoneReq.getPhone())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_PHONE_FORMAT_INCORRECT);
        }

        // 发送验证码
        String smsCode = SecurityStringUtil.getRandomSmsCode();
        String cacheKey = MemberRedisConstant.UPDATE_PWD_BY_PHONE_REDIS_KEY_PREFIX + userDO.getId();
        String smsCodeSecurityKey = SecurityStringUtil.getSmsCodeSecurityKey(cacheKey);

        // 如果验证码已经发送，则不再发送
        BusinessAssertUtil.isFalse(redisUtils.keyExists(smsCodeSecurityKey, RedisConstant.REDIS_USER_INDEX), ResponseCodeEnum.SMS_CODE_ALREADY_EXISTS);

        // 发送验证码
        smsFeignService.sendPlatformLoginForgetPwdSms(phoneReq.getTelCode(), phoneReq.getPhone(), smsCode);
        memberCacheService.setString(cacheKey, smsCode, smsConfig.getExpireSeconds());
        redisUtils.stringSet(smsCodeSecurityKey, "", smsConfig.getIntervalSeconds(), RedisConstant.REDIS_USER_INDEX);
    }

    @Override
    public void sendManageLoginEmailSmsCode(HttpHeaders headers, EmailLoginSmsCode emailReq) {
        memberCacheService.checkPlatformRequestHeader(headers);

        // 查找用户
        UserDO userDO = userRepository.findFirstByEmailAndRelType(emailReq.getEmail(), MemberRelationTypeEnum.PLATFORM.getCode());
        BusinessAssertUtil.notNull(userDO, ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);

        // 发送邮箱验证码
        String smsCode = SecurityStringUtil.getRandomSmsCode();
        String cacheKey = MemberRedisConstant.UPDATE_PWD_BY_EMAIL_REDIS_KEY_PREFIX + userDO.getId();
        String smsCodeSecurityKey = SecurityStringUtil.getSmsCodeSecurityKey(cacheKey);

        // 如果验证码已经发送，则不再发送
        BusinessAssertUtil.isFalse(redisUtils.keyExists(smsCodeSecurityKey, RedisConstant.REDIS_USER_INDEX), ResponseCodeEnum.SMS_CODE_ALREADY_EXISTS);

        // 发送验证码
        memberCacheService.setString(cacheKey, smsCode, memberRefreshConfig.getMailCodeCachedTimeSeconds());
        redisUtils.stringSet(smsCodeSecurityKey, "", smsConfig.getIntervalSeconds(), RedisConstant.REDIS_USER_INDEX);

        String title = MemberStringEnum.DYNAMIC_EMAIL_CODE_TITLE.getName();
        String content = MemberStringEnum.DYNAMIC_EMAIL_CODE_TEMPLATE.getName();

        MailUtil.sendTextMail(title, String.format(content, smsCode), userDO.getEmail());
    }

    @Override
    public PlatformUpdateForgetPasswordResp updatePlatformUserForgetPsw(HttpHeaders headers, PlatformUpdateForgetPasswordReq passwordVO) {
        // 手机号和邮箱不能同时为空
        BusinessAssertUtil.notAllBlank(Arrays.asList(passwordVO.getPhone(), passwordVO.getEmail()).toArray(new CharSequence[0]), ResponseCodeEnum.MC_MS_PHONE_OR_EMAIL_ALL_NULL);

        // 手机验证码和邮箱验证码不能同时为空
        BusinessAssertUtil.notAllBlank(Arrays.asList(passwordVO.getPhoneSmsCode(), passwordVO.getEmailSmsCode()).toArray(new CharSequence[0]), ResponseCodeEnum.MC_MS_PHONE_OR_EMAIL_SMS_CODE_ALL_NULL);

        // 查找用户
        UserDO userDO;
        boolean updateByPhone = false;
        if (StringUtils.hasLength(passwordVO.getPhone())) {
            userDO = userRepository.findFirstByPhoneAndRelType(passwordVO.getPhone(), MemberRelationTypeEnum.PLATFORM.getCode());
            updateByPhone = true;
        } else {
            userDO = userRepository.findFirstByEmailAndRelType(passwordVO.getEmail(), MemberRelationTypeEnum.PLATFORM.getCode());
        }

        // 校验用户是否存在
        BusinessAssertUtil.notNull(userDO, ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);

        // 校验验证码
        if (memberRefreshConfig.getCheckSmsCode()) {
            String cacheKey;
            if (updateByPhone) {
                cacheKey = MemberRedisConstant.UPDATE_PWD_BY_PHONE_REDIS_KEY_PREFIX + userDO.getId();
                BusinessAssertUtil.isTrue(Objects.equals(memberCacheService.getString(cacheKey), passwordVO.getPhoneSmsCode()), ResponseCodeEnum.USER_SMSCODE_EXPIRED);
            } else {
                cacheKey = MemberRedisConstant.UPDATE_PWD_BY_EMAIL_REDIS_KEY_PREFIX + userDO.getId();
                BusinessAssertUtil.isTrue(Objects.equals(memberCacheService.getString(cacheKey), passwordVO.getEmailSmsCode()), ResponseCodeEnum.USER_SMSCODE_EXPIRED);
            }

            // 删除使用过的验证码
            eventPublisher.publishEvent(new RedisKeyRemoveEvent(this, Collections.singletonList(cacheKey)));
        }

        // 如果是超管，需要做双重校验，需要手机号和邮箱都有，这边仅返回信息，真正校验在超管改密码接口
        if (UserTypeEnum.ADMIN.getCode().equals(userDO.getUserType())) {
            BusinessAssertUtil.allNotBlank(Arrays.asList(userDO.getPhone(), userDO.getEmail()).toArray(new CharSequence[0]), ResponseCodeEnum.MC_MS_CURRENT_ACCOUNT_HAS_RISKS_PLEASE_NOTIFY_THE_MANAGEMENT);
            return new PlatformUpdateForgetPasswordResp(true, userDO.getPhone(), userDO.getEmail());
        }

        // 设置新密码
        userDO.setPassword(PasswordUtil.tryEncrypt(passwordVO.getNewPassword()));
        userRepository.save(userDO);

        return new PlatformUpdateForgetPasswordResp(false, null, null);
    }

    @Override
    public void updatePlatformManageUserForgetPsw(HttpHeaders headers, PlatformManageUpdateForgetPasswordReq passwordVO) {
        // 查找用户
        UserDO userDO = userRepository.findFirstByPhoneAndRelType(passwordVO.getPhone(), MemberRelationTypeEnum.PLATFORM.getCode());

        // 校验用户是否存在
        BusinessAssertUtil.notNull(userDO, ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);

        // 校验用户邮箱和数据库中存储的是否一致
        BusinessAssertUtil.isTrue(Objects.equals(userDO.getEmail(), passwordVO.getEmail()), ResponseCodeEnum.MC_MS_MANAGE_EMAIL_ERROR);

        // 校验验证码
        if (memberRefreshConfig.getCheckSmsCode()) {
            String phoneCacheKey = MemberRedisConstant.UPDATE_PWD_BY_PHONE_REDIS_KEY_PREFIX + userDO.getId();
            String emailCacheKey = MemberRedisConstant.UPDATE_PWD_BY_EMAIL_REDIS_KEY_PREFIX + userDO.getId();
            BusinessAssertUtil.isTrue(Objects.equals(memberCacheService.getString(phoneCacheKey), passwordVO.getPhoneSmsCode()), ResponseCodeEnum.USER_SMSCODE_EXPIRED);
            BusinessAssertUtil.isTrue(Objects.equals(memberCacheService.getString(emailCacheKey), passwordVO.getEmailSmsCode()), ResponseCodeEnum.USER_SMSCODE_EXPIRED);

            // 删除使用过的验证码
            eventPublisher.publishEvent(new RedisKeyRemoveEvent(this, Arrays.asList(phoneCacheKey, emailCacheKey)));
        }

        userDO.setPassword(PasswordUtil.tryEncrypt(passwordVO.getNewPassword()));
        userRepository.save(userDO);

    }
}
