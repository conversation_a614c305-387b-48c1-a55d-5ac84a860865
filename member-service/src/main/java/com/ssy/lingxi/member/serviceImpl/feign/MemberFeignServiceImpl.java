package com.ssy.lingxi.member.serviceImpl.feign;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.querydsl.core.types.ConstructorExpression;
import com.querydsl.core.types.ExpressionUtils;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.StringTemplate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.ssy.lingxi.common.model.dto.MemberAndRoleIdDTO;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.util.CollectionPageUtil;
import com.ssy.lingxi.common.util.DateTimeUtil;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.component.base.config.BaiTaiMemberProperties;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.*;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.PasswordUtil;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.api.enums.MemberPayPasswordCheckEnum;
import com.ssy.lingxi.member.api.model.req.*;
import com.ssy.lingxi.member.api.model.resp.*;
import com.ssy.lingxi.member.entity.do_.basic.*;
import com.ssy.lingxi.member.entity.do_.branch.MemberBranchDO;
import com.ssy.lingxi.member.entity.do_.levelRight.QMemberLevelConfigDO;
import com.ssy.lingxi.member.entity.do_.levelRight.QMemberLevelRightDO;
import com.ssy.lingxi.member.entity.do_.lifecycle.MemberAfterApprovalLifecycleStagesDO;
import com.ssy.lingxi.member.entity.do_.lifecycle.MemberLifecycleStagesDO;
import com.ssy.lingxi.member.enums.*;
import com.ssy.lingxi.member.model.resp.basic.MemberRegisterTagResp;
import com.ssy.lingxi.member.repository.*;
import com.ssy.lingxi.member.service.IMemberService;
import com.ssy.lingxi.member.service.base.IBaseMemberRegisterDetailService;
import com.ssy.lingxi.member.service.feign.IMemberFeignService;
import com.ssy.lingxi.member.service.web.IMemberBranchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.time.Period;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 会员信息查询对外Feign接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-08-11
 */
@Slf4j
@Service
public class MemberFeignServiceImpl implements IMemberFeignService {
    @Resource
    private MemberRepository memberRepository;

    @Resource
    private MemberRoleRepository roleRepository;

    @Resource
    private MemberRelationRepository relationRepository;

    @Resource
    private IBaseMemberRegisterDetailService baseMemberRegisterDetailService;

    @Resource
    private UserRepository userRepository;

    @Resource
    private JPAQueryFactory jpaQueryFactory;

    @Resource
    private MemberLifecycleStagesRepository memberLifecycleStagesRepository;

    @Resource
    private MemberAfterApprovalLifecycleStagesRepository memberAfterApprovalLifecycleStagesRepository;

    @Resource
    private BaiTaiMemberProperties baiTaiMemberProperties;
    @Resource
    private IMemberService memberService;

    @Resource
    private CorporationRepository corporationRepository;
    @Resource
    private MemberBranchRepository memberBranchRepository;

    private static final QMemberDO qMember = QMemberDO.memberDO;

    /**
     * 新增会员支付策略 - 查询作为服务提供者的会员信息列表
     *
     * @param pageVO 接口参数
     * @return 操作结果
     */
    @Override
    public PageDataResp<MemberFeignPayProviderResultResp> findPayProviderMembers(MemberFeignPayProviderDataReq pageVO) {
        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.PLATFORM.getCode()));
            list.add(criteriaBuilder.equal(root.get("verified").as(Integer.class), MemberValidateStatusEnum.VERIFY_PASSED.getCode()));
            list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), MemberStatusEnum.NORMAL.getCode()));

            Join<Object, Object> subRoleJoin = root.join("subRole", JoinType.LEFT);
            list.add(criteriaBuilder.equal(subRoleJoin.get("roleType").as(Integer.class), RoleTypeEnum.SERVICE_PROVIDER.getCode()));

            Join<Object, Object> subMemberJoin = root.join("subMember", JoinType.LEFT);
            if (StringUtils.hasLength(pageVO.getMemberName())) {
                list.add(criteriaBuilder.like(subMemberJoin.get("name").as(String.class), "%" + pageVO.getMemberName().trim() + "%"));
            }

            if (!CollectionUtils.isEmpty(pageVO.getItems())) {
                List<Predicate> orList = new ArrayList<>();
                for (MemberFeignPayProviderDataReq.MemberItem item : pageVO.getItems()) {
                    orList.add(criteriaBuilder.and(criteriaBuilder.equal(root.get("subMemberId").as(Long.class), item.getMemberId()), criteriaBuilder.equal(root.get("subRoleId").as(Long.class), item.getRoleId())));
                }
                Predicate[] orP = new Predicate[orList.size()];
                list.add(criteriaBuilder.or(orList.toArray(orP)));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("id").descending());
        Page<MemberRelationDO> pageList = relationRepository.findAll(specification, page);

        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(relationDO -> {
            MemberFeignPayProviderResultResp resultVO = new MemberFeignPayProviderResultResp();
            resultVO.setId(relationDO.getId());
            resultVO.setMemberId(relationDO.getSubMemberId());
            resultVO.setMemberName(relationDO.getSubMember().getName());
            resultVO.setRoleId(relationDO.getSubRoleId());
            resultVO.setRoleName(relationDO.getSubRoleName());
            resultVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getSubRole().getMemberType()));
            resultVO.setLevel(relationDO.getLevelRight() == null ? 0 :relationDO.getLevelRight().getLevel());
            resultVO.setLevelTag(relationDO.getLevelRight() == null ? "" : relationDO.getLevelRight().getLevelTag());
            return resultVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 新增会员支付策略 - 选择适用会员
     *
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberFeignPayProviderResultResp> pageNewPayProviderMembers(MemberFeignPayProviderExcludeDataReq pageVO) {
        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.PLATFORM.getCode()));
            list.add(criteriaBuilder.equal(root.get("verified").as(Integer.class), MemberValidateStatusEnum.VERIFY_PASSED.getCode()));
            list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), MemberStatusEnum.NORMAL.getCode()));

            Join<Object, Object> subRoleJoin = root.join("subRole", JoinType.LEFT);
            list.add(criteriaBuilder.equal(subRoleJoin.get("roleType").as(Integer.class), RoleTypeEnum.SERVICE_PROVIDER.getCode()));

            if (StringUtils.hasLength(pageVO.getMemberName())) {
                Join<Object, Object> subMemberJoin = root.join("subMember", JoinType.LEFT);
                list.add(criteriaBuilder.like(subMemberJoin.get("name").as(String.class), "%" + pageVO.getMemberName().trim() + "%"));
            }

            if (pageVO.getRoleId() != null && !pageVO.getRoleId().equals(0L)) {
                list.add(criteriaBuilder.equal(root.get("subRoleId").as(Long.class), pageVO.getRoleId()));
            }

            if (pageVO.getMemberType() != null && !pageVO.getMemberType().equals(0L)) {
                list.add(criteriaBuilder.equal(subRoleJoin.get("memberType"), pageVO.getMemberType()));
            }

            if (pageVO.getLevel() != null && !pageVO.getLevel().equals(0)) {
                Join<Object, Object> levelRightJoin = root.join("levelRight", JoinType.LEFT);
                list.add(criteriaBuilder.equal(levelRightJoin.get("level").as(Integer.class), pageVO.getLevel()));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };


        List<MemberRelationDO> relationDOList = relationRepository.findAll(specification, Sort.by("id").ascending());

        if (!CollectionUtils.isEmpty(pageVO.getExcludeList())) {
            relationDOList = relationDOList.stream().filter(relationDO -> pageVO.getExcludeList().stream().noneMatch(exist -> exist.getMemberId().equals(relationDO.getSubMemberId()) && exist.getRoleId().equals(relationDO.getSubRoleId()))).collect(Collectors.toList());
        }

        int totalCount = relationDOList.size();

        List<MemberRelationDO> pageList = CollectionPageUtil.pageList(relationDOList, totalCount, pageVO.getCurrent(), pageVO.getPageSize());

        return new PageDataResp<>((long) totalCount, pageList.stream().map(relationDO -> {
            MemberFeignPayProviderResultResp resultVO = new MemberFeignPayProviderResultResp();
            resultVO.setId(relationDO.getId());
            resultVO.setMemberId(relationDO.getSubMemberId());
            resultVO.setMemberName(relationDO.getSubMember().getName());
            resultVO.setRoleId(relationDO.getSubRoleId());
            resultVO.setRoleName(relationDO.getSubRoleName());
            resultVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getSubRole().getMemberType()));
            resultVO.setLevel(relationDO.getLevelRight().getLevel());
            resultVO.setLevelTag(relationDO.getLevelRight().getLevelTag());
            return resultVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 模板服务 - 模糊查询会员信息列表
     *
     * @param listVO 接口参数
     * @return 操作结果
     */
    @Override
    public List<MemberFeignListResultResp> findMembersFromTemplateService(MemberFeignListReq listVO) {
        String name = StringUtils.hasLength(listVO.getName()) ? listVO.getName().trim() : "";

        List<MemberDO> memberDOList = memberRepository.findByNameContainsAndIdIn(name, listVO.getMemberIds());

        return memberDOList.stream().filter(memberDO -> !MemberRegisterSourceEnum.FROM_MANAGE_PLATFORM.getCode().equals(memberDO.getSource())).map(memberDO -> {
            MemberFeignListResultResp resultVO = new MemberFeignListResultResp();
            resultVO.setMemberId(memberDO.getId());
            resultVO.setName(StringUtils.hasLength(memberDO.getName()) ? memberDO.getName() : "");
            return resultVO;
        }).collect(Collectors.toList());
    }

    /**
     * 根据会员Id和角色Id，查询上属会员列表（不区分企业会员、渠道会员）
     * <p>其中等级为当前会员在其上级会员和上级会员角色下的等级</p>
     *
     * @param feignVO 接口参数
     * @return 操作结果
     */
    @Override
    public List<MemberFeignQueryResp> listUpperMembers(MemberFeignReq feignVO) {
        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("subMemberId").as(Long.class), feignVO.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("subRoleId").as(Long.class), feignVO.getRoleId()));
            list.add(criteriaBuilder.equal(root.get("verified").as(Integer.class), MemberValidateStatusEnum.VERIFY_PASSED.getCode()));
            Join<Object, Object> subRoleJoin = root.join("role", JoinType.LEFT);
            list.add(criteriaBuilder.equal(subRoleJoin.get("roleType").as(Integer.class), RoleTypeEnum.SERVICE_PROVIDER.getCode()));

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        return relationRepository.findAll(specification).stream().map(relationDO -> {
            MemberFeignQueryResp queryVO = new MemberFeignQueryResp();
            queryVO.setMemberId(relationDO.getMemberId());
            queryVO.setMemberName(relationDO.getMember().getName());
            queryVO.setLogo(StringUtils.hasLength(relationDO.getMember().getLogo()) ? relationDO.getMember().getLogo() : "");
            queryVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getSubRole().getMemberType()));
            queryVO.setRoleId(relationDO.getRoleId());
            queryVO.setRoleName(relationDO.getSubRoleName());
            queryVO.setLevelTag(relationDO.getLevelRight().getLevelTag());

            return queryVO;
        }).collect(Collectors.toList());
    }

    /**
     * 根据会员Id和角色Id，查询下属会员列表（不区分企业会员、渠道会员）
     * <p>其中等级为下级会员和角色在当前会员和角色下的等级</p>
     *
     * @param feignVO 接口参数
     * @return 操作结果
     */
    @Override
    public List<MemberFeignQueryResp> listLowerMembers(MemberFeignSubReq feignVO) {
        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("memberId").as(Long.class), feignVO.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("roleId").as(Long.class), feignVO.getRoleId()));
            Join<Object, Object> subRoleJoin = root.join("subRole", JoinType.LEFT);
//            list.add(criteriaBuilder.equal(subRoleJoin.get("roleType").as(Integer.class), RoleTypeEnum.SERVICE_CONSUMER.getCode()));

            if (feignVO.getMemberType() != null && !feignVO.getMemberType().equals(0L)) {
                list.add(criteriaBuilder.equal(subRoleJoin.get("memberType"), feignVO.getMemberType()));
            }

            if (feignVO.getSubRoleId() != null && !feignVO.getSubRoleId().equals(0L)) {
                list.add(criteriaBuilder.equal(root.get("subRoleId").as(Long.class), feignVO.getSubRoleId()));
            }

            if (feignVO.getLevel() != null && !feignVO.getLevel().equals(0)) {
                Join<Object, Object> levelJoin = root.join("levelRight", JoinType.LEFT);
                list.add(criteriaBuilder.equal(levelJoin.get("level").as(Integer.class), feignVO.getLevel()));
            }

            if (StringUtils.hasLength(feignVO.getSubMemberName())) {
                Join<Object, Object> subMemberJoin = root.join("subMember", JoinType.LEFT);
                list.add(criteriaBuilder.like(subMemberJoin.get("name").as(String.class), feignVO.getSubMemberName().trim()));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        return relationRepository.findAll(specification).stream().map(relationDO -> {
            MemberFeignQueryResp queryVO = new MemberFeignQueryResp();
            queryVO.setMemberId(relationDO.getSubMemberId());
            queryVO.setLogo(StringUtils.hasLength(relationDO.getSubMember().getLogo()) ? relationDO.getSubMember().getLogo() : "");
            queryVO.setMemberName(relationDO.getSubMember().getName());
            queryVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getSubRole().getMemberType()));
            queryVO.setRoleId(relationDO.getSubRoleId());
            queryVO.setRoleName(relationDO.getSubRoleName());
            queryVO.setLevel(relationDO.getLevelRight() == null ? 0 : relationDO.getLevelRight().getLevel());
            queryVO.setLevelTag(relationDO.getLevelRight() == null ? "" : relationDO.getLevelRight().getLevelTag());
            queryVO.setStatus(relationDO.getStatus());
            return queryVO;
        }).collect(Collectors.toList());
    }

    /**
     * 根据会员Id和角色Id，查询“审核通过”的下属会员列表（不区分渠道类型、角色类型）
     * <p>其中等级为下级会员和角色在当前会员和角色下的等级</p>
     *
     * @param feignVO 接口参数
     * @return 查询结果
     */
    @Override
    public List<MemberFeignQueryResp> listAllLowerMembers(MemberFeignReq feignVO) {
        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("memberId").as(Long.class), feignVO.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("roleId").as(Long.class), feignVO.getRoleId()));
            list.add(criteriaBuilder.equal(root.get("verified").as(Integer.class), MemberValidateStatusEnum.VERIFY_PASSED.getCode()));

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        return relationRepository.findAll(specification).stream().map(relationDO -> {
            MemberFeignQueryResp queryVO = new MemberFeignQueryResp();
            queryVO.setMemberId(relationDO.getSubMemberId());
            queryVO.setLogo(StringUtils.hasLength(relationDO.getSubMember().getLogo()) ? relationDO.getSubMember().getLogo() : "");
            queryVO.setMemberName(relationDO.getSubMember().getName());
            queryVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getSubRole().getMemberType()));
            queryVO.setRoleId(relationDO.getSubRoleId());
            queryVO.setRoleName(relationDO.getSubRoleName());
            queryVO.setLevelTag(relationDO.getLevelRight().getLevelTag());
            queryVO.setLevel(relationDO.getLevelRight().getLevel());
            return queryVO;
        }).collect(Collectors.toList());
    }

    /**
     * 根据会员Id和角色Id，查询“审核通过”的下级会员列表（角色类型为服务提供者）
     * <p>返回结果中的等级为下级会员和角色在当前会员和角色下的等级</p>
     * @param feignVO 接口参数
     * @return 查询结果
     */
    @Override
    public List<MemberFeignQueryResp> listSubProviderLowerMembers(MemberFeignReq feignVO) {
        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("memberId").as(Long.class), feignVO.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("roleId").as(Long.class), feignVO.getRoleId()));
            list.add(criteriaBuilder.equal(root.get("verified").as(Integer.class), MemberValidateStatusEnum.VERIFY_PASSED.getCode()));
            Join<Object, Object> subRoleJoin = root.join("subRole", JoinType.LEFT);
            list.add(criteriaBuilder.equal(subRoleJoin.get("roleType").as(Integer.class), RoleTypeEnum.SERVICE_PROVIDER.getCode()));

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        return relationRepository.findAll(specification).stream().map(relationDO -> {
            MemberFeignQueryResp queryVO = new MemberFeignQueryResp();
            queryVO.setMemberId(relationDO.getSubMemberId());
            queryVO.setLogo(StringUtils.hasLength(relationDO.getSubMember().getLogo()) ? relationDO.getSubMember().getLogo() : "");
            queryVO.setMemberName(relationDO.getSubMember().getName());
            queryVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getSubRole().getMemberType()));
            queryVO.setRoleId(relationDO.getSubRoleId());
            queryVO.setRoleName(relationDO.getSubRoleName());
            queryVO.setLevelTag(relationDO.getLevelRight().getLevelTag());
            queryVO.setLevel(relationDO.getLevelRight().getLevel());
            return queryVO;
        }).collect(Collectors.toList());
    }

    /**
     * 根据会员Id、角色Id、上级会员Id、上级会员角色Id，查询会员信息
     * <p>其中等级为当前会员在其上级会员和上级会员角色下的等级</p>
     *
     * @param feignVO 接口参数
     * @return 操作结果
     */
    @Override
    public MemberFeignQueryResp getMemberInfo(MemberRelationFeignReq feignVO) {
        MemberRelationDO relationDO = relationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(feignVO.getUpperMemberId(), feignVO.getUpperRoleId(), feignVO.getMemberId(), feignVO.getRoleId());
        if (relationDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        MemberFeignQueryResp queryVO = new MemberFeignQueryResp();
        queryVO.setSubMemberName(relationDO.getSubMember().getName());
        queryVO.setLogo(StringUtils.hasLength(relationDO.getMember().getLogo()) ? relationDO.getMember().getLogo() : "");
        queryVO.setMemberId(relationDO.getSubMemberId());
        queryVO.setMemberName(relationDO.getMember().getName());
        queryVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getSubRole().getMemberType()));
        queryVO.setRoleId(relationDO.getSubRoleId());
        queryVO.setRoleName(relationDO.getSubRoleName());
        queryVO.setLevelTag(relationDO.getLevelRight().getLevelTag());

        return queryVO;
    }

    /**
     * 根据会员Id，查询会员注册信息
     *
     * @param idVO 接口参数
     * @return 操作结果
     */
    @Override
    public MemberFeignRegisterQueryResp getMemberRegisterInfo(MemberFeignIdReq idVO) {
        MemberRegisterTagResp result = baseMemberRegisterDetailService.getMemberRegisterTagDetail(idVO.getMemberId());

        MemberFeignRegisterQueryResp queryVO = new MemberFeignRegisterQueryResp();
        if (result != null) {
            queryVO.setMemberId(result.getMemberId());
            queryVO.setName(result.getName());
            queryVO.setPhone(result.getPhone());
            queryVO.setIdentityCard(result.getIdentityCard());
            queryVO.setIdentityCardFront(result.getIdentityCardFront());
            queryVO.setIdentityCardBack(result.getIdentityCardBack());
            queryVO.setLegalPersonName(result.getLegalPersonName());
            queryVO.setLegalPersonPhone(result.getLegalPersonPhone());
            queryVO.setLegalPersonIdentityCardNo(result.getLegalPersonIdentityCardNo());
            queryVO.setLegalPersonIdentityCardFront(result.getLegalPersonIdentityCardFront());
            queryVO.setLegalPersonIdentityCardBack(result.getLegalPersonIdentityCardBack());
            queryVO.setUnifiedCreditCode(result.getUnifiedCreditCode());
            queryVO.setRegisteredCapital(result.getRegisteredCapital());
            queryVO.setEstablishmentDate(result.getEstablishmentDate());
            queryVO.setBusinessLicence(result.getBusinessLicence());
            queryVO.setRegisterArea(result.getRegisterArea());
            queryVO.setRegisterAddress(result.getRegisterAddress());
            queryVO.setProvinceName(result.getProvinceName());
            queryVO.setCityName(result.getCityName());
            queryVO.setLogo(result.getLogo());
        }

        return queryVO;
    }

    /**
     * 根据会员Id列表，查询具有“服务提供者”角色类型的会员信息
     *
     * @param queryVO 接口参数
     * @return 操作结果
     */
    @Override
    public List<MemberFeignBatchByIdQueryResp> batchFindMembersByIdList(MemberFeignBatchByIdReq queryVO) {
        //todo 这个接口有问题
        //先查找所有下级会员
        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("memberId").as(Long.class), queryVO.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.OTHER.getCode()));
            list.add(criteriaBuilder.equal(root.get("verified").as(Integer.class), MemberValidateStatusEnum.VERIFY_PASSED.getCode()));
            list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), MemberStatusEnum.NORMAL.getCode()));
            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        List<Long> subMemberIdList = relationRepository.findAll(specification).stream().map(MemberRelationDO::getSubMemberId).distinct().collect(Collectors.toList());

        List<MemberRoleDO> roleDOList = roleRepository.findByRoleType(RoleTypeEnum.SERVICE_PROVIDER.getCode());
        List<Long> roleIds = roleDOList.stream().map(MemberRoleDO::getId).collect(Collectors.toList());

        specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.PLATFORM.getCode()));
            list.add(criteriaBuilder.equal(root.get("verified").as(Integer.class), MemberValidateStatusEnum.VERIFY_PASSED.getCode()));
            list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), MemberStatusEnum.NORMAL.getCode()));

            Expression<Long> subMemberIdInExp = root.get("subMemberId").as(Long.class);
            list.add(subMemberIdInExp.in(subMemberIdList));

            Expression<Long> subRoleIdInExp = root.get("subRoleId").as(Long.class);
            list.add(subMemberIdInExp.in(roleIds));

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        List<MemberRelationDO> memberRelationDOList = relationRepository.findAll(specification);
        return memberRelationDOList.stream().map(relationDO -> {
            MemberFeignBatchByIdQueryResp feignQueryVO = new MemberFeignBatchByIdQueryResp();
            feignQueryVO.setMemberId(relationDO.getSubMemberId());
            feignQueryVO.setMemberName(relationDO.getSubMember().getName());
            feignQueryVO.setRoleId(relationDO.getSubRoleId());
            feignQueryVO.setRoleName(relationDO.getSubRoleName());
            feignQueryVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getSubMemberTypeEnum()));
            feignQueryVO.setLevelTag(relationDO.getLevelRight().getLevelTag());
            feignQueryVO.setIsSubMember(subMemberIdList.contains(relationDO.getSubMemberId()) ? 1 : 0);
            return feignQueryVO;
        }).collect(Collectors.toList());

    }

    /**
     * 根据会员Id列表，查询具有“服务提供者”角色类型的“平台会员”信息
     *
     * @param idsVO 接口参数
     * @return 查询结果
     */
    @Override
    public List<MemberFeignShopQueryResp> batchFindServiceProviderMemberByIdList(MemberFeignIdsReq idsVO) {
        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();

            list.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.PLATFORM.getCode()));

            Join<Object, Object> subRoleJoin = root.join("subRole", JoinType.LEFT);
            list.add(criteriaBuilder.equal(subRoleJoin.get("roleType").as(Integer.class), RoleTypeEnum.SERVICE_PROVIDER.getCode()));

            Expression<Long> subMemberIdInExp = root.get("subMemberId").as(Long.class);
            list.add(subMemberIdInExp.in(idsVO.getMemberIds()));

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        List<MemberRelationDO> relationDOList = relationRepository.findAll(specification, Sort.by("id").ascending());

        return relationDOList.stream().map(relationDO -> {
            MemberFeignShopQueryResp queryVO = new MemberFeignShopQueryResp();
            queryVO.setMemberId(relationDO.getSubMemberId());
            queryVO.setName(relationDO.getSubMember().getName());
            queryVO.setRoleId(relationDO.getSubRoleId());
            queryVO.setRoleName(relationDO.getSubRoleName());
            queryVO.setLevel(relationDO.getLevelRight().getLevel());
            queryVO.setLevelTag(relationDO.getLevelRight().getLevelTag());
            queryVO.setStatus(relationDO.getStatus());
            queryVO.setRoleStatus(relationDO.getSubRole().getStatus());
            queryVO.setOuterStatus(relationDO.getOuterStatus());
            queryVO.setOuterStatusName(MemberOuterStatusEnum.getCodeMsg(relationDO.getOuterStatus()));
            queryVO.setRegisterYears(Period.between(relationDO.getCreateTime().toLocalDate(), LocalDateTime.now().toLocalDate()).getYears() + 1);
            queryVO.setCreditPoint(relationDO.getCredit().getCreditPoint());
            queryVO.setAvgTradeCommentStar(relationDO.getCredit().getAvgTradeCommentStar());
            return queryVO;
        }).collect(Collectors.toList());
    }

    /**
     * 根据会员名称，查询具有“服务提供者”角色类型的企业会员、企业个人会员的Id列表
     *
     * @param nameVO 接口参数
     * @return 查询结果
     */
    @Override
    public MemberFeignIdsResultResp findMerchantAndProviderMemberIdListByName(MemberFeignNameReq nameVO) {
        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();

            list.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.PLATFORM.getCode()));
            Join<Object, Object> subRoleJoin = root.join("subRole", JoinType.LEFT);
            list.add(criteriaBuilder.equal(subRoleJoin.get("roleType").as(Integer.class), RoleTypeEnum.SERVICE_PROVIDER.getCode()));
            list.add(criteriaBuilder.or(criteriaBuilder.equal(subRoleJoin.get("memberType").as(Integer.class), MemberTypeEnum.MERCHANT.getCode()), criteriaBuilder.equal(subRoleJoin.get("memberType").as(Integer.class), MemberTypeEnum.MERCHANT_PERSONAL.getCode())));

            Join<Object, Object> subMemberJoin = root.join("subMember", JoinType.LEFT);
            list.add(criteriaBuilder.like(subMemberJoin.get("name").as(String.class), "%" + nameVO.getName().trim() + "%"));

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        List<MemberRelationDO> relationDOList = relationRepository.findAll(specification, Sort.by("id").ascending());

        MemberFeignIdsResultResp resultVO = new MemberFeignIdsResultResp();
        resultVO.setMemberIds(relationDOList.stream().map(MemberRelationDO::getSubMemberId).distinct().collect(Collectors.toList()));

        return resultVO;
    }

    /**
     * 根据会员Id、角色Id查询平台会员信息
     * <p>其中等级为当前会员的平台等级</p>
     *
     * @param memberFeignReqList 接口参数
     * @return 查询结果
     **/
    @Override
    public List<MemberFeignQueryResp> listPlatformMembers(List<MemberFeignReq> memberFeignReqList) {
        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.PLATFORM.getCode()));

            //Or查询
            if (!CollectionUtils.isEmpty(memberFeignReqList)) {
                List<Predicate> orList = new ArrayList<>();
                for (MemberFeignReq item : memberFeignReqList) {
                    orList.add(criteriaBuilder.and(criteriaBuilder.equal(root.get("subMemberId").as(Long.class), item.getMemberId()), criteriaBuilder.equal(root.get("subRoleId").as(Long.class), item.getRoleId())));
                }
                Predicate[] orP = new Predicate[orList.size()];
                list.add(criteriaBuilder.or(orList.toArray(orP)));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        return relationRepository.findAll(specification).stream().map(relationDO -> {
            MemberFeignQueryResp queryVO = new MemberFeignQueryResp();
            queryVO.setSubMemberName("");
            queryVO.setMemberId(relationDO.getSubMemberId());
            queryVO.setMemberName(relationDO.getSubMember().getName());
            queryVO.setLogo(StringUtils.hasLength(relationDO.getSubMember().getLogo()) ? relationDO.getSubMember().getLogo() : "");
            queryVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getSubRole().getMemberType()));
            queryVO.setRoleId(relationDO.getSubRoleId());
            queryVO.setRoleName(relationDO.getSubRole().getRoleName());
            queryVO.setLevelTag(relationDO.getLevelRight() == null ? "" : relationDO.getLevelRight().getLevelTag());
            return queryVO;
        }).collect(Collectors.toList());
    }

    /**
     * 根据会员Id、角色Id查询平台会员信息
     **/
    @Override
    public List<MemberFeignQueryResp> listMembers(List<MemberFeignReq> memberFeignReqList) {
        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();

            //Or查询
            if (!CollectionUtils.isEmpty(memberFeignReqList)) {
                List<Predicate> orList = new ArrayList<>();
                for (MemberFeignReq item : memberFeignReqList) {
                    orList.add(criteriaBuilder.and(criteriaBuilder.equal(root.get("subMemberId").as(Long.class), item.getMemberId()), criteriaBuilder.equal(root.get("subRoleId").as(Long.class), item.getRoleId())));
                }
                Predicate[] orP = new Predicate[orList.size()];
                list.add(criteriaBuilder.or(orList.toArray(orP)));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        return relationRepository.findAll(specification).stream().map(relationDO -> {
            MemberFeignQueryResp queryVO = new MemberFeignQueryResp();
            queryVO.setSubMemberName("");
            queryVO.setMemberId(relationDO.getSubMemberId());
            queryVO.setMemberName(relationDO.getSubMember().getName());
            queryVO.setLogo(StringUtils.hasLength(relationDO.getSubMember().getLogo()) ? relationDO.getSubMember().getLogo() : "");
            queryVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getSubRole().getMemberType()));
            queryVO.setRoleId(relationDO.getSubRoleId());
            queryVO.setRoleName(relationDO.getSubRole().getRoleName());
            queryVO.setLevelTag(relationDO.getLevelRight() == null ? "" : relationDO.getLevelRight().getLevelTag());
            queryVO.setStatus(relationDO.getStatus());

            return queryVO;
        }).collect(Collectors.toList());
    }

    /**
     * 根据会员Id和加密后的支付密码，校验支付密码
     *
     * @param checkVO 接口参数
     * @return 查询结果
     */
    @Override
    public MemberFeignPayPswCheckResultResp checkMemberPayPassword(MemberFeignPayPswCheckReq checkVO) {
        MemberDO memberDO = memberRepository.findById(checkVO.getMemberId()).orElse(null);
        if (memberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        MemberFeignPayPswCheckResultResp resultVO = new MemberFeignPayPswCheckResultResp();
        if (StringUtils.hasLength(memberDO.getPayPassword())) {
            if (PasswordUtil.checkPassword(memberDO.getPayPassword(), checkVO.getPayPassword())) {
                resultVO.setCheckResult(MemberPayPasswordCheckEnum.CORRECT.getCode());
            } else {
                resultVO.setCheckResult(MemberPayPasswordCheckEnum.INCORRECT.getCode());
            }
        } else {
            resultVO.setCheckResult(MemberPayPasswordCheckEnum.NOT_SET.getCode());
        }

        return resultVO;
    }

    /**
     * 批量查询会员Logo
     *
     * @param memberIds 会员id列表
     * @return 查询结果
     */
    @Override
    public List<MemberFeignLogoResp> getMemberLogos(List<Long> memberIds) {
        if (CollectionUtils.isEmpty(memberIds)) {
            return new ArrayList<>();
        }

        List<MemberDO> memberDOList = memberRepository.findAllById(memberIds);

        return memberDOList.stream().map(memberDO -> {
            MemberFeignLogoResp logoVO = new MemberFeignLogoResp();
            logoVO.setMemberId(memberDO.getId());
            logoVO.setName(memberDO.getName());
            logoVO.setLogo(StringUtils.hasLength(memberDO.getLogo()) ? memberDO.getLogo() : "");
            logoVO.setPhone(memberDO.getPhone());
            return logoVO;
        }).collect(Collectors.toList());
    }

    /**
     * 批量查询会员角色名称
     *
     * @param roleIds 会员角色Id列表
     * @return 查询结果
     */
    @Override
    public List<MemberFeignRoleResp> getRoles(List<Long> roleIds) {
        return roleRepository.findAllById(roleIds).stream().map(memberRoleDO -> {
            MemberFeignRoleResp roleVO = new MemberFeignRoleResp();
            roleVO.setRoleId(memberRoleDO.getId());
            roleVO.setRoleName(memberRoleDO.getRoleName());
            return roleVO;
        }).collect(Collectors.toList());
    }

    /**
     * 根据会员ID批量查询IM客服用户
     * @param memberIds 会员id列表
     * @return 查询结果
     */
    @Override
    public List<MemberFeignImUserResp> getImUsers(List<Long> memberIds) {
        if (CollectionUtils.isEmpty(memberIds)) {
            throw new BusinessException("参数不能空");
        }

        List<MemberDO> memberDOList = memberRepository.findAllById(memberIds);
        List<UserDO> userDOList = userRepository.findByMemberIn(memberDOList);
        return userDOList.stream().filter(a ->
                a.getRoles().stream().anyMatch(b -> b.getHasImAuth() != null && b.getHasImAuth().equals(1))).map(a -> {
            MemberFeignImUserResp memberFeignImUserResp = new MemberFeignImUserResp();
            memberFeignImUserResp.setMemberId(a.getMember().getId());
            memberFeignImUserResp.setMemberName(a.getMember().getName());
            memberFeignImUserResp.setMemberLogo(a.getMember().getLogo());
            memberFeignImUserResp.setUserId(a.getId());
            memberFeignImUserResp.setUserLogo(a.getLogo());
            memberFeignImUserResp.setUserName(a.getName());
            memberFeignImUserResp.setUserRoleName(a.getRoles().stream().findFirst().orElse(new UserRoleDO()).getRoleName());
            return memberFeignImUserResp;
        }).collect(Collectors.toList());
    }

    /**
     * 根据用户ID批量查询用户信息
     * @param userIds 用户id列表
     * @return 查询结果
     */
    @Override
    public List<MemberFeignUserResp> getUsers(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            throw new BusinessException("参数不能空");
        }

        List<UserDO> userDOList = userRepository.findAllById(userIds);
        if (CollectionUtils.isEmpty(userDOList)) {
            throw new BusinessException("数据记录不存在");
        }

        return userDOList.stream().map(a -> {
            MemberFeignUserResp memberFeignUserResp = new MemberFeignUserResp();
            memberFeignUserResp.setMemberId(a.getMember().getId());
            memberFeignUserResp.setMemberName(a.getMember().getName());
            memberFeignUserResp.setMemberLogo(a.getMember().getLogo());
            memberFeignUserResp.setUserId(a.getId());
            memberFeignUserResp.setUserLogo(a.getLogo());
            memberFeignUserResp.setUserName(a.getName());
            return memberFeignUserResp;
        }).collect(Collectors.toList());
    }

    @Override
    public MemberFeignUserResp getUser(Long userId) {
        if (Objects.isNull(userId)) {
            throw new BusinessException("参数不能空");
        }

        UserDO userDO = userRepository.findById(userId).orElseThrow(() -> new BusinessException("数据记录不存在"));
        MemberFeignUserResp memberFeignUserResp = new MemberFeignUserResp();
        memberFeignUserResp.setMemberId(userDO.getMember().getId());
        memberFeignUserResp.setMemberName(userDO.getMember().getName());
        memberFeignUserResp.setMemberLogo(userDO.getMember().getLogo());
        memberFeignUserResp.setUserId(userDO.getId());
        memberFeignUserResp.setUserLogo(userDO.getLogo());
        memberFeignUserResp.setUserName(userDO.getName());
        memberFeignUserResp.setTelCode(userDO.getTelCode());
        memberFeignUserResp.setPhone(userDO.getPhone());
        return memberFeignUserResp;
    }

    /**
     * 订单服务，查询流程规则适用会员列表
     *
     * @param feignVO 接口参数
     * @return 查询结果
     */
    @Override
    public List<MemberFeignPageQueryResp> findPlatformMembers(List<MemberFeignReq> feignVO) {
        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.PLATFORM.getCode()));

            //动态拼接多个Or查询
            List<Predicate> orList = new ArrayList<>();
            for (MemberFeignReq member : feignVO) {
                orList.add(criteriaBuilder.and(criteriaBuilder.equal(root.get("subMemberId").as(Long.class), member.getMemberId()), criteriaBuilder.equal(root.get("subRoleId").as(Long.class), member.getRoleId())));
            }
            Predicate[] orP = new Predicate[orList.size()];
            list.add(criteriaBuilder.or(orList.toArray(orP)));

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };
        return relationRepository.findAll(specification).stream().map(relation -> {
            MemberFeignPageQueryResp queryVO = new MemberFeignPageQueryResp();
            queryVO.setMemberId(relation.getSubMemberId());
            queryVO.setRoleId(relation.getSubRoleId());
            queryVO.setName(relation.getSubMember().getName());
            queryVO.setRoleName(relation.getSubRole().getRoleName());
            queryVO.setMemberTypeName(MemberTypeEnum.getName(relation.getSubRole().getMemberType()));
            queryVO.setLevel(relation.getLevelRight() == null ? 0 : relation.getLevelRight().getLevel());
            queryVO.setLevelTag(relation.getLevelRight() == null ? "" : relation.getLevelRight().getLevelTag());
            return queryVO;
        }).collect(Collectors.toList());
    }

    /**
     * 订单服务，商户支付参数配置，查询平台服务提供者企业会员列表
     *
     * @param paymentVO 接口参数
     * @return 查询结果
     **/
    @Override
    public List<MemberFeignPageQueryResp> findProviderMerchant(MemberFeignPaymentReq paymentVO) {
        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.PLATFORM.getCode()));

            list.add(criteriaBuilder.equal(root.get("subRole").get("roleType").as(Integer.class), RoleTypeEnum.SERVICE_PROVIDER.getCode()));
            list.add(criteriaBuilder.or(criteriaBuilder.equal(root.get("subRole").get("memberType").as(Integer.class), MemberTypeEnum.MERCHANT.getCode()), criteriaBuilder.equal(root.get("subRole").get("memberType").as(Integer.class), MemberTypeEnum.MERCHANT_PERSONAL.getCode())));

            if (StringUtils.hasText(paymentVO.getName())) {
                list.add(criteriaBuilder.like(root.get("subMember").get("name").as(String.class), "%" + paymentVO.getName().trim() + "%"));
            }

            //动态拼接多个Or查询
            if(!CollectionUtils.isEmpty(paymentVO.getMembers())) {
                List<Predicate> orList = paymentVO.getMembers().stream().map(member -> criteriaBuilder.and(criteriaBuilder.equal(root.get("subMemberId").as(Long.class), member.getMemberId()), criteriaBuilder.equal(root.get("subRoleId").as(Long.class), member.getRoleId()))).collect(Collectors.toList());
                Predicate[] orP = new Predicate[orList.size()];
                list.add(criteriaBuilder.or(orList.toArray(orP)));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        return relationRepository.findAll(specification).stream().map(relation -> {
            MemberFeignPageQueryResp queryVO = new MemberFeignPageQueryResp();
            queryVO.setMemberId(relation.getSubMemberId());
            queryVO.setRoleId(relation.getSubRoleId());
            queryVO.setName(relation.getSubMember().getName());
            queryVO.setRoleName(relation.getSubRole().getRoleName());
            queryVO.setMemberTypeName(MemberTypeEnum.getName(relation.getSubRole().getMemberType()));
            queryVO.setLevel(relation.getLevelRight() == null ? 0 : relation.getLevelRight().getLevel());
            queryVO.setLevelTag(relation.getLevelRight() == null ? "" : relation.getLevelRight().getLevelTag());
            return queryVO;
        }).collect(Collectors.toList());
    }

    /**
     * 查询是否下级会员
     *
     * @param feignVO 接口参数
     * @return 查询结果
     */
    @Override
    public Boolean isSubMember(MemberRelationFeignReq feignVO) {
        MemberRelationDO relationDO = relationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(feignVO.getUpperMemberId(), feignVO.getUpperRoleId(), feignVO.getMemberId(), feignVO.getRoleId());
        if (relationDO == null || relationDO.getVerified().equals(MemberValidateStatusEnum.VERIFY_NOT_PASSED.getCode()) || relationDO.getStatus().equals(MemberStatusEnum.ELIMINATED.getCode()) || relationDO.getStatus().equals(MemberStatusEnum.BLACK_LIST.getCode())) {
            return Boolean.FALSE;
        } else {
            return Boolean.TRUE;
        }
    }

    @Override
    public List<AtSubMemberSuitableMemberResp> listAbilitySubMemberSuitableMember(MemberAndUpperMembersReq memberAndUpperMembersReq) {
        log.info("查询下级会员适用会员, 参数: {}", JSONObject.toJSONString(memberAndUpperMembersReq));
        // 查找与上级会员的关系
        List<MemberRelationDO> relationDOList = relationRepository.findBySubMemberIdAndSubRoleIdAndVerified(memberAndUpperMembersReq.getSubMemberId(), memberAndUpperMembersReq.getSubRoleId(), MemberValidateStatusEnum.VERIFY_PASSED.getCode());
        if (CollectionUtils.isEmpty(relationDOList)) {
            WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }
        List<AtSubMemberSuitableMemberResp> resultList = new ArrayList<>();
        for (MemberFeignReq upperMemberRole : memberAndUpperMembersReq.getUpperMemberRoles()) {
            AtSubMemberSuitableMemberResp atSubMemberSuitableMemberResp = new AtSubMemberSuitableMemberResp();

            MemberRelationDO relationDO = relationDOList.stream().filter(e -> e.getMemberId().equals(upperMemberRole.getMemberId()) && e.getRoleId().equals(upperMemberRole.getRoleId())).findFirst().orElse(null);
            if (Objects.isNull(relationDO)) {
                // 没有找到关系,继续寻找与平台的关系
                MemberRelationDO platformRelationDO = relationDOList.stream().filter(e -> MemberLevelTypeEnum.PLATFORM.getCode().equals(e.getSubMemberLevelTypeEnum())).findFirst().orElse(null);
                if (Objects.nonNull(platformRelationDO)) {
                    LocalDateTime depositTime = platformRelationDO.getDepositTime();
                    if (Objects.nonNull(depositTime)) {
                        // 入库时间在当前时间前
                        if (depositTime.isBefore(DateTimeUtil.getTodayBeginLocal())) {
                            atSubMemberSuitableMemberResp.setOldUser(true);
                        } else {
                            atSubMemberSuitableMemberResp.setNewUser(true);
                        }
                    }
                }
            } else {
                // 找到关系, 判断是否为商户会员或渠道会员
                if (MemberLevelTypeEnum.MERCHANT.getCode().equals(relationDO.getSubMemberLevelTypeEnum())) {
                    // 上级为商户会员或上级为渠道会员
                    atSubMemberSuitableMemberResp.setSubMemberRelation(true);
                    LocalDateTime depositTime = relationDO.getDepositTime();
                    if (Objects.nonNull(depositTime)) {
                        // 入库时间在当前时间前
                        if (depositTime.isBefore(DateTimeUtil.getTodayBeginLocal())) {
                            atSubMemberSuitableMemberResp.setOldMember(true);
                        } else {
                            atSubMemberSuitableMemberResp.setNewMember(true);
                        }
                    }

                    atSubMemberSuitableMemberResp.setMemberType(relationDO.getSubMemberTypeEnum());
                    atSubMemberSuitableMemberResp.setRoleType(relationDO.getSubRole().getRoleType());
                    atSubMemberSuitableMemberResp.setLevelType(relationDO.getSubMemberLevelTypeEnum());
                    atSubMemberSuitableMemberResp.setLevel(relationDO.getLevelRight() == null ? 0 : relationDO.getLevelRight().getLevel());
                }
            }

            atSubMemberSuitableMemberResp.setUpperMemberId(upperMemberRole.getMemberId());
            atSubMemberSuitableMemberResp.setUpperRoleId(upperMemberRole.getRoleId());
            atSubMemberSuitableMemberResp.setMemberId(memberAndUpperMembersReq.getSubMemberId());
            atSubMemberSuitableMemberResp.setRoleId(memberAndUpperMembersReq.getSubRoleId());

            resultList.add(atSubMemberSuitableMemberResp);
        }
        return resultList;
    }

    @Override
    public PfSubMemberSuitableMemberResp getPlatformSubMemberSuitableMember(MemberFeignReq memberFeignReq) {
        // 查询所有下级会员的平台关系
        MemberRelationDO relationDO = relationRepository.findFirstBySubMemberIdAndSubRoleIdAndRelTypeAndVerified(memberFeignReq.getMemberId(), memberFeignReq.getRoleId(), MemberRelationTypeEnum.PLATFORM.getCode(), MemberValidateStatusEnum.VERIFY_PASSED.getCode());
        if (Objects.isNull(relationDO)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        PfSubMemberSuitableMemberResp pfSubMemberSuitableMemberResp = new PfSubMemberSuitableMemberResp();

        LocalDateTime depositTime = relationDO.getDepositTime();
        if (Objects.nonNull(depositTime)) {
            // 入库时间在当前时间前
            if (depositTime.isBefore(DateTimeUtil.getTodayBeginLocal())) {
                pfSubMemberSuitableMemberResp.setOldMember(true);
            } else {
                pfSubMemberSuitableMemberResp.setNewMember(true);
            }
        }

        pfSubMemberSuitableMemberResp.setMemberId(relationDO.getSubMemberId());
        pfSubMemberSuitableMemberResp.setRoleId(relationDO.getSubRoleId());
        pfSubMemberSuitableMemberResp.setMemberType(relationDO.getSubMemberTypeEnum());
        pfSubMemberSuitableMemberResp.setRoleType(relationDO.getSubRole().getRoleType());
        pfSubMemberSuitableMemberResp.setLevel(relationDO.getLevelRight() == null ? 0 : relationDO.getLevelRight().getLevel());
        pfSubMemberSuitableMemberResp.setLevelType(relationDO.getSubMemberLevelTypeEnum());

        return pfSubMemberSuitableMemberResp;
    }

    /**
     * 所有服务通用 - 查询平台规则配置
     *
     * @param feignVO 接口参数
     * @return 查询结果
     */
    @Override
    public List<MemberRuleDetailResp> findMemberRules(MemberRuleDetailFeignReq feignVO) {
        QBaseMemberRuleDO qBaseMemberRule = QBaseMemberRuleDO.baseMemberRuleDO;
        JPAQuery<MemberRuleDetailResp> query = jpaQueryFactory.select(Projections.constructor(MemberRuleDetailResp.class, qBaseMemberRule.ruleType, qBaseMemberRule.ruleName, qBaseMemberRule.methodCode, qBaseMemberRule.methodName, qBaseMemberRule.status))
                .from(qBaseMemberRule);

        if(NumberUtil.notNullOrZero(feignVO.getRuleType())) {
            query.where(qBaseMemberRule.ruleType.eq(feignVO.getRuleType()));
        }

        if(NumberUtil.notNullOrZero(feignVO.getMethodCode())) {
            query.where(qBaseMemberRule.methodCode.eq(feignVO.getMethodCode()));
        }

        if(NumberUtil.notNullOrZero(feignVO.getStatus())) {
            query.where(qBaseMemberRule.status.eq(feignVO.getStatus()));
        }

        return query.fetch();
    }

    /**
     * 根据会员关系，查询业务员Id
     *
     * @param feignVO 接口参数
     * @return 查询结果
     */
    @Override
    public List<MemberSalesFeignResp> findMemberSales(List<MemberFeignRelationReq> feignVO) {
        QMemberUserChannelDO qMemberUserChannel = QMemberUserChannelDO.memberUserChannelDO;

        //Step 1: 拼接查询条件
        com.querydsl.core.types.Predicate[] predicates = feignVO.stream().map(feign -> qMemberUserChannel.memberId.eq(feign.getUpperMemberId()).and(qMemberUserChannel.roleId.eq(feign.getUpperRoleId())).and(qMemberUserChannel.subMemberId.eq(feign.getSubMemberId())).and(qMemberUserChannel.subRoleId.eq(feign.getSubRoleId()))).toArray(com.querydsl.core.types.Predicate[]::new);
        List<MemberUserChannelDO> channels = jpaQueryFactory.select(qMemberUserChannel).from(qMemberUserChannel).where(ExpressionUtils.anyOf(predicates)).fetch();

        return feignVO.stream().map(feign -> {
            MemberSalesFeignResp salesFeignVO = new MemberSalesFeignResp();
            salesFeignVO.setMemberId(feign.getUpperMemberId());
            salesFeignVO.setRoleId(feign.getUpperRoleId());
            salesFeignVO.setSubMemberId(feign.getSubMemberId());
            salesFeignVO.setSubRoleId(feign.getSubRoleId());
            salesFeignVO.setUserId(channels.stream().filter(channel -> channel.getMemberId().equals(feign.getUpperMemberId()) && channel.getRoleId().equals(feign.getUpperRoleId()) && channel.getSubMemberId().equals(feign.getSubMemberId()) && channel.getSubRoleId().equals(feign.getSubRoleId())).map(MemberUserChannelDO::getUserId).findFirst().orElse(0L));
            return salesFeignVO;
        }).collect(Collectors.toList());
    }

    /**
     * 筛选角色类型为服务提供者的会员
     * @param members 会员Id和角色Id的缓存实体
     * @return 筛选结果
     */
    @Override
    public List<MemberAndRoleIdDTO> screenMemberUser(List<MemberAndRoleIdDTO> members) {
        Specification<MemberRoleDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();

            list.add(criteriaBuilder.in(root.get("id")).value(members.stream().map(MemberAndRoleIdDTO::getRoleId).collect(Collectors.toList())));
            list.add(criteriaBuilder.equal(root.get("roleType").as(Integer.class), RoleTypeEnum.SERVICE_PROVIDER.getCode()));

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        List<MemberRoleDO> roleDOS = roleRepository.findAll(specification);
        List<Long> ids = roleDOS.stream().map(MemberRoleDO::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(roleDOS)){
            return new ArrayList<>();
        }
        return members.stream().filter(merberAndRoleId -> ids.contains(merberAndRoleId.getRoleId())).collect(Collectors.toList());
    }

    /**
     * 查询指定会员的生命周期规则配置相关
     * @param memberGetLifecycleFeignReq 接口参数
     * @return 查询结果
     */
    @Override
    public MemberLifecycleRuleQueryResp getLifecycle(MemberGetLifecycleFeignReq memberGetLifecycleFeignReq) {
        MemberLifecycleRuleQueryResp queryVO = new MemberLifecycleRuleQueryResp();
        //拿到生命周期阶段以及规则
        List<MemberLifecycleStagesDO> memberLifecycleStagesDOS = memberLifecycleStagesRepository.findByMemberIdAndRoleIdAndRoleTag(memberGetLifecycleFeignReq.getMemberId(), memberGetLifecycleFeignReq.getMemberId(), memberGetLifecycleFeignReq.getRoleTag());
        ArrayList<MemberLifecycleStagesAndRuleResp> memberLifecycleStagesAndRuleVOS = new ArrayList<>();
        for (MemberLifecycleStagesDO memberLifecycleStagesDO : memberLifecycleStagesDOS) {
            MemberLifecycleStagesAndRuleResp lifecycleStagesAndRuleQueryVO = new MemberLifecycleStagesAndRuleResp();
            lifecycleStagesAndRuleQueryVO.setLifecycleStagesId(memberLifecycleStagesDO.getId());
            lifecycleStagesAndRuleQueryVO.setLifecycleStagesNum(memberLifecycleStagesDO.getLifecycleStagesNum());
            lifecycleStagesAndRuleQueryVO.setLifecycleStagesName(memberLifecycleStagesDO.getLifecycleStagesName());
//            lifecycleStagesAndRuleQueryVO.setLifecycleStagesRuleIds(memberLifecycleStagesDO.getLifecycleStagesRuleIds());
            memberLifecycleStagesAndRuleVOS.add(lifecycleStagesAndRuleQueryVO);
        }
        queryVO.setLifecycle(memberLifecycleStagesAndRuleVOS);

        //拿到入库身后通过后的生命周期阶段
        MemberAfterApprovalLifecycleStagesDO memberAfterApprovalLifecycleStagesDO = memberAfterApprovalLifecycleStagesRepository.findByMemberIdAndAndRoleIdAndRoleTag(memberGetLifecycleFeignReq.getMemberId(), memberGetLifecycleFeignReq.getMemberId(), memberGetLifecycleFeignReq.getRoleTag());
        queryVO.setAfterApprovalLifecycleStagesId(memberAfterApprovalLifecycleStagesDO.getId());
        queryVO.setAfterApprovalLifecycleStagesNum(memberAfterApprovalLifecycleStagesDO.getLifecycleStagesNum());

        return queryVO;
    }

    /**
     * 查找所有供应商
     */
    @Override
    public List<MemberAndRoleIdDTO> allSupplierList() {
        List<MemberDO> allMember = memberRepository.findAll();
        List<MemberAndRoleIdDTO> memberAndRoles = allMember.stream().flatMap(f -> f.getMemberRoles().stream().map(m -> {
            MemberAndRoleIdDTO dto = new MemberAndRoleIdDTO();
            dto.setMemberId(f.getId());
            dto.setRoleId(m.getId());
            return dto;
        })).collect(Collectors.toList());

        QMemberRoleDO qMemberRoleDO= QMemberRoleDO.memberRoleDO;
        JPAQuery<Long> query = jpaQueryFactory.select(Projections.constructor(Long.class, qMemberRoleDO.id))
                .from(qMemberRoleDO)
                .where(qMemberRoleDO.roleType.eq(RoleTypeEnum.SERVICE_PROVIDER.getCode()));

        List<Long> roleIdList = query.fetch();
        return memberAndRoles.stream().filter(m -> ObjectUtil.isNotNull(m.getRoleId()) && roleIdList.contains(m.getRoleId())).collect(Collectors.toList());
    }

    /**
     * 合同能力- 查询会员合同模版所需参数
     *
     * @param queryVOS 接口参数
     * @return 查询结果
     */
    @Override
    public List<MemberContractDetailResp> getMemberContractDetail(List<MemberContractQueryReq> queryVOS) {
        List<MemberContractDetailResp> contractDetailVOS = new ArrayList<>();
        for (MemberContractQueryReq queryVO : queryVOS) {
            MemberContractDetailResp detailVO = new MemberContractDetailResp();
            MemberRegisterTagResp data = baseMemberRegisterDetailService.getMemberRegisterTagDetail(queryVO.getMemberId());
            if (Objects.isNull(data)){
                continue;
            }
            detailVO.setMemberId(queryVO.getMemberId());
            detailVO.setOrgName(data.getName());
            detailVO.setRepName(data.getLegalPersonName());
            detailVO.setMobile(data.getLegalPersonPhone());
            detailVO.setAddress(data.getRegisterArea() + data.getRegisterAddress());
            contractDetailVOS.add(detailVO);
        }

        return contractDetailVOS;
    }

    /**
     * （营销服务v3）优惠券发券 - 发券时查询会员列表
     *
     * @param req 接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MarketingMemberFeignResp> pageMarketingCouponMembers(MarketingMemberFeignReq req) {
        QMemberRelationDO qMemberRelation = QMemberRelationDO.memberRelationDO;
        QMemberLevelRightDO qMemberLevelRight = QMemberLevelRightDO.memberLevelRightDO;
        QMemberLevelConfigDO qMemberLevelConfig = QMemberLevelConfigDO.memberLevelConfigDO;
        QMemberDO qMember = QMemberDO.memberDO;
        QMemberRoleDO qMemberRole = QMemberRoleDO.memberRoleDO;

        ConstructorExpression<MarketingMemberFeignResp> projection = Projections.constructor(MarketingMemberFeignResp.class,
                qMemberRelation.id, qMemberRelation.createTime, qMemberRelation.subMemberId, qMemberRelation.subRoleId, qMember.name, qMemberRole.roleName, qMemberRelation.subMemberTypeEnum, qMemberLevelConfig.id, qMemberLevelConfig.levelTag
                );

        JPAQuery<MarketingMemberFeignResp> query = jpaQueryFactory.select(projection)
                .from(qMemberRelation)
                // 状态正常
                .where(qMemberRelation.status.eq(MemberStatusEnum.NORMAL.getCode()))
                .leftJoin(qMember).on(qMemberRelation.subMember.id.eq(qMember.id))
                .leftJoin(qMemberLevelRight).on(qMemberRelation.id.eq(qMemberLevelRight.relation.id))
                .leftJoin(qMemberLevelConfig).on(qMemberLevelRight.levelConfig.id.eq(qMemberLevelConfig.id))
                .leftJoin(qMemberRole).on(qMemberRelation.subRoleId.eq(qMemberRole.id))
                .orderBy(qMemberRelation.id.desc())
                .limit(req.getPageSize()).offset(req.getCurrentOffset());

        if (NumberUtil.isPositive(req.getSubMemberId())) {
            query.where(qMemberRelation.subMemberId.eq(req.getSubMemberId()));
        }

        if (StringUtils.hasLength(req.getSubMemberName())) {
            query.where(qMember.name.like("%".concat(req.getSubMemberName().trim()).concat("%")));
        }

        if (NumberUtil.isPositive(req.getSubMemberType())) {
            query.where(qMemberRelation.subMemberTypeEnum.eq(req.getSubMemberType()));
        }

        if (!CollectionUtils.isEmpty(req.getExcludes())) {
            //使用PostgreSQL的字符串拼接运算符
            StringTemplate stringTemplate = Expressions.stringTemplate("{0} || ',' || {1}", qMemberRelation.subMemberId, qMemberRelation.subRoleId);
            query.where(stringTemplate.notIn(req.getExcludes().stream().map(member -> String.valueOf(member.getMemberId()).concat(",").concat(String.valueOf(member.getRoleId()))).collect(Collectors.toList())));
        }

        // 平台后台   -  优惠券有等级配置： 查询平台会员
        //             优惠券无等级配置： 查询平台会员
        // 供应商会员 -  优惠券有等级配置： 查询下级会员
        //             优惠券无等级配置： 查询平台会员
        if (req.getPlatform()) {
            query.where(qMemberRelation.relType.eq(MemberRelationTypeEnum.PLATFORM.getCode()).and(qMemberRelation.innerStatus.eq(PlatformInnerStatusEnum.VERIFY_PASSED.getCode())));
            if (!CollectionUtils.isEmpty(req.getMemberLevelIds())) {
                query.where(qMemberLevelConfig.id.in(req.getMemberLevelIds()));
            }
        } else {
            if (CollectionUtils.isEmpty(req.getMemberLevelIds())) {
                query.where(qMemberRelation.relType.eq(MemberRelationTypeEnum.PLATFORM.getCode()).and(qMemberRelation.innerStatus.eq(PlatformInnerStatusEnum.VERIFY_PASSED.getCode())));
            } else {
                query.where(qMemberRelation.memberId.eq(req.getUpperMemberId())
                        .and(qMemberRelation.roleId.eq(req.getUpperRoleId())
                                .and(qMemberRelation.innerStatus.eq(MemberInnerStatusEnum.VERIFY_PASSED.getCode()).or(qMemberRelation.innerStatus.eq(MemberInnerStatusEnum.MODIFY_PASSED.getCode())))
                                .and(qMemberLevelConfig.id.in(req.getMemberLevelIds()))));
            }
        }

        long totalCount = query.fetchCount();
        if (totalCount == 0) {
            return new PageDataResp<>(totalCount, new ArrayList<>());
        }

        return new PageDataResp<>(totalCount, query.fetch());
    }

    /**
     * （营销服务v3）优惠券发券 - 查询已经发券的会员列表
     *
     * @param req 接口参数
     * @return 查询结果
     */
    @Override
    public List<MarketingMemberFeignResp> findMarketingCouponMembers(MarketingDistributeMemberFeignReq req) {
        QMemberRelationDO qMemberRelation = QMemberRelationDO.memberRelationDO;
        QMemberLevelRightDO qMemberLevelRight = QMemberLevelRightDO.memberLevelRightDO;
        QMemberLevelConfigDO qMemberLevelConfig = QMemberLevelConfigDO.memberLevelConfigDO;
        QMemberDO qMember = QMemberDO.memberDO;
        QMemberRoleDO qMemberRole = QMemberRoleDO.memberRoleDO;

        ConstructorExpression<MarketingMemberFeignResp> projection = Projections.constructor(MarketingMemberFeignResp.class,
                qMemberRelation.id, qMemberRelation.createTime, qMemberRelation.subMemberId, qMemberRelation.subRoleId, qMember.name, qMemberRole.roleName, qMemberRelation.subMemberTypeEnum, qMemberLevelConfig.id, qMemberLevelConfig.levelTag
        );

        JPAQuery<MarketingMemberFeignResp> query = jpaQueryFactory.select(projection)
                .from(qMemberRelation)
                .leftJoin(qMember).on(qMemberRelation.subMember.id.eq(qMember.id))
                .leftJoin(qMemberLevelRight).on(qMemberRelation.id.eq(qMemberLevelRight.relation.id))
                .leftJoin(qMemberLevelConfig).on(qMemberLevelRight.levelConfig.id.eq(qMemberLevelConfig.id))
                .leftJoin(qMemberRole).on(qMemberRelation.subRoleId.eq(qMemberRole.id));

        // 是否查询平台会员
        if (req.getPlatform()) {
            query.where(qMemberRelation.relType.eq(MemberRelationTypeEnum.PLATFORM.getCode()));
        } else {
            query.where(qMemberRelation.memberId.eq(req.getUpperMemberId())
                    .and(qMemberRelation.roleId.eq(req.getUpperRoleId())));
        }

        //指定的下级会员
        if (!CollectionUtils.isEmpty(req.getSubMembers())) {
            //使用PostgreSQL的字符串拼接运算符
            StringTemplate stringTemplate = Expressions.stringTemplate("{0} || ',' || {1}", qMemberRelation.subMemberId, qMemberRelation.subRoleId);
            query.where(stringTemplate.in(req.getSubMembers().stream().map(member -> String.valueOf(member.getMemberId()).concat(",").concat(String.valueOf(member.getRoleId()))).collect(Collectors.toList())));
        }

        return query.fetch();
    }

    @Override
    public MemberNameResp getMemberName(MemberFeignIdReq req) {
        String memberName = jpaQueryFactory.select(qMember.name).from(qMember).where(qMember.id.eq(req.getMemberId())).fetchFirst();
        return new MemberNameResp(req.getMemberId(), memberName);
    }

    /**
     * 查询商家会员id，和角色id
     * @return 查询结果
     */
    @Override
    public MemberFeignMerchantResp getMerchantMember() {
        MemberRelationDO memberRelationDO = relationRepository.findFirstByIsMerchant(true);
        MemberFeignMerchantResp memberFeignMerchantResp = new MemberFeignMerchantResp();
        memberFeignMerchantResp.setMemberId(memberRelationDO.getSubMemberId());
        memberFeignMerchantResp.setRoleId(memberRelationDO.getSubRoleId());
        return memberFeignMerchantResp;
    }

    @Override
    public MemberFeignCodeRes findByCode(MemberFeignCodeReq memberFeignCodeReq) {
        Specification<MemberDO> spec =  (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("code"), memberFeignCodeReq.getCode()));
            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };
        MemberDO memberDO = memberRepository.findOne(spec).orElse(null);
        if(!Objects.isNull(memberDO)){
            return BeanUtil.copyProperties(memberDO, MemberFeignCodeRes.class);
        }
        return null;
    }

    /**
     * 根据会员ID查询会员信息
     *
     * @param getMemberByIdReq 接口参数
     * @return 查询结果
     */
    @Override
    public MemberFeignCodeRes findMemberById(GetMemberByIdReq getMemberByIdReq) {
        MemberDO memberDO = memberRepository.findById(getMemberByIdReq.getMemberId()).orElse(null);
        if(!Objects.isNull(memberDO)){
            return BeanUtil.copyProperties(memberDO, MemberFeignCodeRes.class);
        }
        return null;
    }

    /**
     * 根据会员id，查询会员信息
     *
     * @param commonIdListReq 会员id
     * @return 查询结果
     */
    @Override
    public List<MemberFeignCodeRes> findMemberByIdList(CommonIdListReq commonIdListReq) {
        List<MemberDO> memberDOList = memberRepository.findAllById(commonIdListReq.getIdList());
        if (CollUtil.isEmpty(memberDOList)) {
            return Collections.emptyList();
        }
        return BeanUtil.copyToList(memberDOList, MemberFeignCodeRes.class);
    }

    /**
     * 查询商家信息和超级管理员信息
     * @return 查询结果
     */
    @Override
    public MemberFeignMerchantAndAdminResp getMerchantAndAdminInfo() {
        UserDO userDO = userRepository.findFirstByMemberIdAndUserType(baiTaiMemberProperties.getSelfMemberId(), UserTypeEnum.ADMIN.getCode());
        MemberFeignMerchantAndAdminResp memberFeignMerchantAndAdminResp = new MemberFeignMerchantAndAdminResp();
        memberFeignMerchantAndAdminResp.setUserId(userDO.getId());
        memberFeignMerchantAndAdminResp.setUserName(userDO.getName());
        memberFeignMerchantAndAdminResp.setOrgName(ObjectUtil.isNotEmpty(userDO.getOrg()) ? userDO.getOrg().getTitle() : "");
        memberFeignMerchantAndAdminResp.setJobTitle(userDO.getJobTitle());
        memberFeignMerchantAndAdminResp.setUserAccount(userDO.getAccount());
        Optional<MemberRoleDO> roleDO = roleRepository.findById(baiTaiMemberProperties.getSelfBuyerRoleId());
        if (roleDO.isPresent()) {
            memberFeignMerchantAndAdminResp.setMemberRoleName(roleDO.get().getRoleName());
        }
        return memberFeignMerchantAndAdminResp;
    }

    @Override
    public List<MemberInfoResp> findByCorporationIds(List<Long> corporationIds) {
        return memberService.findByCorporationIds(corporationIds);
    }

    /**
     * 根据会员id，会员code
     *
     * @param memberId 会员id
     * @return 查询结果
     */
    @Override
    public String findByMemberId(CommonIdReq memberId) {
        MemberDO memberDO = memberRepository.findById(memberId.getId()).orElse(null);
        return memberDO.getCode();
    }

    @Override
    public List<MemberBrandInfoResp> findMemberBrandByIds(CommonIdListReq commonIdListReq) {
        List<MemberBranchDO> memberBranchDOS = memberBranchRepository.findAllByIdIn(commonIdListReq.getIdList());
        if(CollectionUtils.isEmpty(memberBranchDOS)){
           return null;
        }
        return memberBranchDOS.stream().map(branchDO -> {
            MemberBrandInfoResp  memberBrandInfoResp = new MemberBrandInfoResp();
            memberBrandInfoResp.setBrandId(branchDO.getBrandId());
            memberBrandInfoResp.setMemberId(branchDO.getMemberId());
            memberBrandInfoResp.setBranchId(branchDO.getId());
            memberBrandInfoResp.setRoleId(branchDO.getMemberRoleId());
            return memberBrandInfoResp;
        }).collect(Collectors.toList());
    }

    @Override
    public MemberBrandInfoResp findMemberBrandById(CommonIdReq commonIdReq) {
        MemberBranchDO memberBranchDO = memberBranchRepository.findById(commonIdReq.getId()).orElseThrow(()->new BusinessException(ResponseCodeEnum.MAN_HEADER_BRANCH_NOT_EXIST));
        if(memberBranchDO.getMemberId() == null){
            throw new BusinessException(ResponseCodeEnum.MAN_HEADER_BRANCH_RELATION_MEMBER_NOT_EXIST);
        }
        MemberBrandInfoResp  memberBrandInfoResp = new MemberBrandInfoResp();
        memberBrandInfoResp.setBrandId(memberBranchDO.getBrandId());
        memberBrandInfoResp.setMemberId(memberBranchDO.getMemberId());
        memberBrandInfoResp.setBranchId(memberBranchDO.getId());
        memberBrandInfoResp.setRoleId(memberBranchDO.getMemberRoleId());
        memberBrandInfoResp.setBrandName(memberBranchDO.getName());
        memberBrandInfoResp.setBrandCode(memberBranchDO.getBrandCode());
        MemberDO memberDO = memberRepository.findById(memberBranchDO.getMemberId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST));
        memberBrandInfoResp.setMemberCode(memberDO.getCode());
        memberBrandInfoResp.setMemberName(memberDO.getName());
        return memberBrandInfoResp;
    }


    /**
     * 根据买家会员id，查询（营业证统一编码，手机号，邮箱）
     * @param memberId 会员id
     * @return 查询结果
     */
    @Override
    public MemberInsuredIdNoResp findBusinessInfoByMemberId(CommonIdReq memberId) {
        MemberDO memberDO = memberRepository.findById(memberId.getId()).orElseThrow(() -> new BusinessException("会员不存在"));
        CorporationDO corporationDO = corporationRepository.findById(memberDO.getCorporationId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_CORPORATION_DOES_NOT_EXIST));

        MemberInsuredIdNoResp memberInsuredIdNoResp = new MemberInsuredIdNoResp();
        memberInsuredIdNoResp.setName(corporationDO.getName());
        memberInsuredIdNoResp.setInsuredIdNo(corporationDO.getUnifiedSocialCode());
        memberInsuredIdNoResp.setInsuredMobilePhone(memberDO.getPhone());
        memberInsuredIdNoResp.setEmail(memberDO.getEmail());
        return memberInsuredIdNoResp;
    }
}
