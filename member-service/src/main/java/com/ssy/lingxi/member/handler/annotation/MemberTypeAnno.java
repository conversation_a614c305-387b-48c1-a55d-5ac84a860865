package com.ssy.lingxi.member.handler.annotation;


import com.ssy.lingxi.member.handler.validator.MemberTypeValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 校验整数参数值是否定义在MemberTypeEnum的枚举值范围内
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-06-13
 */
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE, ElementType.CONSTRUCTOR, ElementType.PARAMETER})
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = {MemberTypeValidator.class})
public @interface MemberTypeAnno {
    boolean required() default true;

    String message() default "参数值不在枚举定义范围内";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
