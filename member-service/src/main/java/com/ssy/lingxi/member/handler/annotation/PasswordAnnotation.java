package com.ssy.lingxi.member.handler.annotation;

import com.ssy.lingxi.member.handler.validator.PasswordValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 密码校验注解
 */
@Target({ElementType.TYPE, ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = PasswordValidator.class)
public @interface PasswordAnnotation {
    boolean required() default true;

    int min() default 8;

    int max() default 16;

    boolean upperCase() default true;
    boolean lowerCase() default true;
    boolean numbers() default true;
    boolean chars() default true;

    String message() default "密码为8-16个字符，由英文大小写字母、数字、符号组成，请勿使用简单密码";

    Class<?>[] groups() default { };

    Class<? extends Payload>[] payload() default { };
}
