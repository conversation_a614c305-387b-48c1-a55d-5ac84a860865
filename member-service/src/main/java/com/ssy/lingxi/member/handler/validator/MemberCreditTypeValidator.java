package com.ssy.lingxi.member.handler.validator;

import com.ssy.lingxi.member.api.enums.MemberCreditTypeEnum;
import com.ssy.lingxi.member.handler.annotation.MemberCreditTypeAnno;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Arrays;

/**
 * 会员信用评估规则校验注解校验类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-07-09
 */
public class MemberCreditTypeValidator implements ConstraintValidator<MemberCreditTypeAnno, Integer> {
    private boolean required = false;

    @Override
    public void initialize(MemberCreditTypeAnno constraintAnnotation) {
        required = constraintAnnotation.required();
    }

    @Override
    public boolean isValid(Integer value, ConstraintValidatorContext context) {
        if(!required) {
            return true;
        }

        if(value == null) {
            return false;
        }
        return Arrays.stream(MemberCreditTypeEnum.values()).anyMatch(c -> c.getCode().equals(value));
    }
}
