package com.ssy.lingxi.member.handler.validator;

import com.ssy.lingxi.common.util.IDCardNumberUtil;
import com.ssy.lingxi.member.handler.annotation.IDCardNumberAnnotation;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * 身份证号码注解校验实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-06-19
 */
public class IDCardNumberValidator implements ConstraintValidator<IDCardNumberAnnotation, String> {
    private boolean required;

    @Override
    public void initialize(IDCardNumberAnnotation constraintAnnotation) {
        required = constraintAnnotation.required();
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if(!required) {
            return true;
        }
        return IDCardNumberUtil.isIDNumber(value);
    }
}
